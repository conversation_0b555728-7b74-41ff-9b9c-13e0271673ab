FROM nginx:1.28-alpine

RUN apk update
RUN apk add --update nodejs npm

WORKDIR /analysisframework/client

COPY client/package.json .
COPY client/.npmrc .

RUN npm install --legacy-peer-deps
RUN npm audit fix --legacy-peer-deps; exit 0

COPY client /analysisframework/client

ENV REACT_APP_STAGE production

RUN npm run build

COPY setup/docker/production/nginx.conf-frontend /etc/nginx/conf.d/default.conf

RUN mv /analysisframework/client/build/* /usr/share/nginx/html
