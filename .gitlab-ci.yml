image: docker:24.0.6

services:
    - docker:24.0.6-dind

stages:
    - build

before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY

stagingbuild:
    stage: build

    script:
        - docker build -f setup/docker/staging/Dockerfile-analysisframework-staging -t registry.gitlab.com/streetscooter/analytics/analyseframework/staging .

    after_script:
        - docker push registry.gitlab.com/streetscooter/analytics/analyseframework/staging

    tags:
        - gitlab-org-docker

    only:
        - development

productionbuild:
    stage: build

    script:
        - docker build -f setup/docker/production/Dockerfile-analysisframework-production -t registry.gitlab.com/streetscooter/analytics/analyseframework/production .

    after_script:
        - docker push registry.gitlab.com/streetscooter/analytics/analyseframework/production

    tags:
        - gitlab-org-docker

    only:
        - master
        - tags
