version: "3.7"
services:
    frontend:
        build:
            context: .
            dockerfile: setup/docker/development/Dockerfile-analysisframework-development

        image: analysisframework/development/frontend

        container_name: analysisframework

        volumes:
            - type: bind
              source: "./client/craco.config.js"
              target: "/analysisframework/client/craco.config.js"

            - type: bind
              source: "./client/jsconfig.json"
              target: "/analysisframework/client/jsconfig.json"

            - type: bind
              source: "./client/.eslintrc"
              target: "/analysisframework/client/.eslintrc"

            - type: bind
              source: "./client/.eslintignore"
              target: "/analysisframework/client/.eslintignore"

            - type: bind
              source: "./client/.prettierignore"
              target: "/analysisframework/client/.prettierignore"

            - type: bind
              source: "./client/.prettierrc"
              target: "/analysisframework/client/.prettierrc"

            - type: bind
              source: "./client/src"
              target: "/analysisframework/client/src"

            - type: bind
              source: "./client/public"
              target: "/analysisframework/client/public"

        ports:
            - "3000:3000"

        command: npm run start

        tty: true
