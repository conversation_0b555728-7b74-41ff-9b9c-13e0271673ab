# Analyseframework

Das Analyseframework ist ein Frontend um diverse Fahrzeugdaten einzusehen.

Instanzen:

[Production](https://analysis.streetscooter-cloud-system.eu/)

[Staging](https://analysis-staging.streetscooter-cloud-system.eu)

## Login

Ein Login muss von jemandem erstellt werden der ausreichende Rechte hat.

## Entwicklung

Um selbst am Analyseframework (Frontend) zu entwickeln sollte eine lokale Instanz gestartet werden, das geht mit docker-compose.

```
docker-compose up
```

Die lokale Instanz nutzt standardmäßig die datalayer and keycloak Instanzen des Staging-Systems.

## Einrichtung der Entwicklungsumgebung mit Hot Reload unter Windows 10

1.  Get Local Admin from IT (via Ticket / Service Desk)
1.  **VS Code** download & install:

    https://code.visualstudio.com/download

1.  **Git** Extension download & install:

    https://www.git-scm.com/download/win (64 Bit)

1.  **Docker Desktop** download & install:

    https://www.docker.com/products/docker-desktop/

1.  To finish Docker Desktop installation restart your PC

1.  Open **Power Shell** in Admin Mode and execute the following commands

        wsl --install Ubuntu --web-download
        wsl -s Ubuntu

    select a user name and a password

        wsl -l

    this should show in the resulting list at least

        Ubuntu (Standard)

    1. Trouble shooting hints
       1. Activate virtualization in BIOS
       1. Make sure wsl is up to date `wsl --version` (Version `2.2.4` or higher and Kernel `5.15.153.1` or higher)
       1. execute `wsl --shutdown` and `wsl --update`

1.  Open **Docker Desktop** in Admin Mode and enable WSL integration for the Ubuntu distro
1.  Open **VS Code** in Admin Mode
1.  Install **git** extension into VS Code
1.  Connect the git installation from VS Code via Preferences --> Settings and search for **git** and add installation folder for `git.exe` in `git.path` in the `settings.json`

            {
                "git.path": "C:/Users/<USER>/AppData/Local/Programs/Git/bin/git.exe",
            }

1.  Via the "tree icon" in **VS Code** clone the repositoy

    1.  Open your browser with the AFW Repository (via gitlab)

        https://gitlab.com/streetscooter/analytics/analyseframework

    1.  Get the clone link (via HTTPS) from Analyseframework gitlab (make sure you pick your branch)
    1.  After login on your local git (e.g. via git credentials manager) the clone should run through and result in the shown tree in the VS Code file explore.

1.  open terminal and execute `wsl` - you are now in the Linux world

1.  Copy your checked out folder (from your windows file system) to your home directory in your Ubuntu container:

        cp -r /mnt/c/Users/<USER>/Documents/Code/analyseframework/ ~/repo/ -v

1.  Click "Open Folder" and open the repository folder

        /home/<USER>/repo

1.  If not already happened, install all necessary extensions vor VS Code, at least:
    -> **Prettier**
    -> **Docker**
    -> **Git**

1.  Make sure prettier works in VS Code go to File -> Preferences -> Settings

    1.  Update the Prettier Path to the repository location:

            /home/<USER>/repo/client/node_modules/prettier/

    1.  if necessary and still not working, install the prettier extension from the dev dependencies by running

            npm install --save-dev --save-exact prettier --legacy-peer-deps

    1.  Prettier should now be running

1.  Open `docker-compose.yml` and run

        docker compose up

    e.g. via right click context menu

1.  Open your browser (Chrome) and navigate to

    http://localhost:3000/

1.  Login with the credentials received (see above)

1.  Before your first comit in git you'll have to add username and email to your `gitconfig`file in Ubuntu

            git config --global user.name "User Name"
            git config --global user.email <EMAIL>

    Happy Coding!
