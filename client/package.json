{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "1.2.14", "@ant-design/plots": "^2.3.2", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/pro-light-svg-icons": "^6.1.1", "@fortawesome/pro-regular-svg-icons": "^6.1.1", "@fortawesome/pro-solid-svg-icons": "^6.1.1", "@fortawesome/react-fontawesome": "^0.1.15", "@react-keycloak/web": "3.4.0", "antd": "5.1.7", "better-react-mathjax": "^1.0.3", "cron-parser": "^4.1.0", "dayjs": "^1.11.7", "dom-to-image": "^2.6.0", "exceljs": "^4.3.0", "i18next": "^21.3.2", "i18next-browser-languagedetector": "^6.1.2", "i18next-xhr-backend": "^3.2.2", "js-file-download": "^0.4.12", "jszip": "^3.9.1", "keycloak-js": "25.0.5", "leaflet": "^1.7.1", "less-vars-to-js": "^1.3.0", "lodash": "^4.17.21", "moment": "^2.29.4", "pondjs": "^0.9.0", "query-string": "^7.0.1", "react": "17.0.2", "react-dom": "17.0.2", "react-i18next": "^11.12.0", "react-leaflet": "^3.2.2", "react-moment": "^1.1.1", "react-number-format": "^4.7.3", "react-query": "^3.27.0", "react-router": "^5.2.1", "react-router-dom": "^5.3.0", "react-scripts": "4.0.3", "react-timeseries-charts": "^0.16.1", "shortid": "^2.2.16", "xlsx": "^0.17.3"}, "scripts": {"translate": "python3 src/app/translations/organize_translations.py", "start": "craco start", "build": "NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" craco build", "test": "craco test", "eject": "react-scripts eject", "prettier:check": "prettier --check \"src/**/*.{js,jsx}\"", "prettier:fix": "prettier --write \"src/**/*.{js,jsx}\"", "analyze": "npx source-map-explorer 'build/static/js/*.js'"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^6.3.0", "craco-alias": "^3.0.1", "craco-antd": "^1.19.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jsdoc": "^46.0.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-react-hooks": "^4.2.0", "jest-prop-type-error": "^1.1.0", "less": "^4.1.2", "less-loader": "^10.1.0", "msw": "^0.35.0", "prettier": "^2.4.1", "styled-components": "^5.3.1"}, "setupFiles": ["jest-prop-type-error"], "overrides": {"caniuse-lite": "1.0.30001632"}}