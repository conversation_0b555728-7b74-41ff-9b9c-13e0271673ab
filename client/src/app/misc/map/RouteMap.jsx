import React from "react";
import PropTypes from "prop-types";
import { GeoJSON } from "react-leaflet";

import { Map } from "misc/map";

import { stsColors } from "styles/colors";

const RouteMap = ({ height = 600, features = [], ...props }) => {
    const calculateCenterAndZoom = () => {
        let minX = 10000;
        let maxX = -10000;
        let minY = 10000;
        let maxY = -10000;

        features.forEach(feature => {
            feature.geometry.coordinates.forEach(xy => {
                minX = minX > xy[0] ? xy[0] : minX;
                minY = minY > xy[1] ? xy[1] : minY;

                maxX = maxX < xy[0] ? xy[0] : maxX;
                maxY = maxY < xy[1] ? xy[1] : maxY;
            });
        });

        let zoom = 14;
        const maxDist = maxX - minX > maxY - minY ? maxX - minX : maxY - minY;
        if (0 <= maxDist && maxDist < 0.005) zoom = 19;
        // ok
        else if (0.005 <= maxDist && maxDist < 0.015) zoom = 15;
        else if (0.015 <= maxDist && maxDist < 0.03) zoom = 16;
        else if (0.03 <= maxDist && maxDist < 0.045) zoom = 13;
        // ok
        else if (0.045 <= maxDist && maxDist < 0.06) zoom = 13;
        // ok
        else if (0.06 <= maxDist && maxDist < 0.075) zoom = 14;
        else if (0.075 <= maxDist && maxDist < 0.1) zoom = 12;
        // ok
        else if (0.1 <= maxDist && maxDist < 0.15) zoom = 12;
        // ok
        else if (0.15 <= maxDist && maxDist < 5) zoom = 11; // ok

        return [[minY + (maxY - minY) / 2, minX + (maxX - minX) / 2], zoom];
    };

    const [center, zoom] = calculateCenterAndZoom();

    return (
        <Map height={height} center={center} zoom={zoom} {...props}>
            <GeoJSON
                data={features}
                style={() => ({
                    color: stsColors.blue2,
                    opacity: 0.8,
                    weight: 2
                })}
            />
        </Map>
    );
};

RouteMap.propTypes = {
    height: PropTypes.number,
    features: PropTypes.array.isRequired
};

export default RouteMap;
