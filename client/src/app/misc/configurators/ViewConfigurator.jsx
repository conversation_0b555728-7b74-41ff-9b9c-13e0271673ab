import React, { useState } from "react";
import { But<PERSON>, Drawer, Form, Tooltip } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PropTypes from "prop-types";

import { TextField } from "misc/fields";

const ViewConfigurator = ({ configuration, onChange = () => null, options = [] }) => {
    const [drawerForm] = Form.useForm();

    const [drawerIsOpen, setDrawerIsOpen] = useState(false);

    const onFinishForm = values => {
        onChange(values);
        setDrawerIsOpen(false);
    };

    return (
        <>
            <Tooltip title={<TextField value="t_view_configuration" />}>
                <Button onClick={() => setDrawerIsOpen(true)} type="primary">
                    <FontAwesomeIcon icon={["far", "sliders-up"]} />
                </Button>
            </Tooltip>
            <Drawer
                title={<TextField value="t_view_configuration" />}
                visible={drawerIsOpen}
                onClose={() => setDrawerIsOpen(false)}
                width={400}
                destroyOnClose
            >
                <Form form={drawerForm} initialValues={configuration} layout="vertical" onFinish={onFinishForm} hideRequiredMark>
                    {options.map(({ name, label, render, ...props }) => (
                        <Form.Item key={name} name={name} label={<TextField value={label} />} {...props}>
                            {render()}
                        </Form.Item>
                    ))}
                    <Button type="primary" htmlType="submit">
                        <TextField value="t_apply" inline />
                    </Button>
                </Form>
            </Drawer>
        </>
    );
};

ViewConfigurator.propTypes = {
    configurations: PropTypes.arrayOf(PropTypes.object),
    onChange: PropTypes.func,
    options: PropTypes.arrayOf(
        PropTypes.shape({
            name: PropTypes.string.isRequired,
            label: PropTypes.string.isRequired,
            render: PropTypes.func.isRequired
        })
    )
};

export default ViewConfigurator;
