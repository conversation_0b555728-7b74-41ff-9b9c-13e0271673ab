import { find } from "lodash";
import { Tree, TreeSelect } from "antd";
import { useTranslation } from "react-i18next";
import React, { useState, useMemo } from "react";
import PropTypes from "prop-types";

import { usePermittedHierarchy } from "misc/api/datalayer";

const PermissionConfigurator = ({ value, onChange, restrictions, allowClear = true, maxTagCount = 5, ...props }) => {
    const { t } = useTranslation();

    const { permittedHierarchy } = usePermittedHierarchy();

    const [searchValue, setSearchValue] = useState("");

    const getFilteredHierarchy = (permittedHierarchy, restrictions) => {
        if (!restrictions) return permittedHierarchy;

        const flattenItems = (items, key) => {
            return items.reduce((flattenedItems, item) => {
                flattenedItems.push(item);
                if (Array.isArray(item[key])) {
                    flattenedItems = flattenedItems.concat(flattenItems(item[key], key));
                }
                return flattenedItems;
            }, []);
        };

        const items = [];

        restrictions.forEach(uuid => items.push(find(flattenItems(permittedHierarchy, "children"), ["uuid", uuid])));

        return items;
    };

    const treeData = useMemo(() => {
        const renderTreeNodes = data =>
            data.map(item => {
                if (item.children) {
                    return (
                        <Tree.TreeNode key={item.uuid} title={item.name} value={item.uuid}>
                            {renderTreeNodes(item.children)}
                        </Tree.TreeNode>
                    );
                }

                return <Tree.TreeNode key={item.uuid} title={item.name} value={item.uuid} />;
            });

        return renderTreeNodes(getFilteredHierarchy(permittedHierarchy, restrictions));
    }, [permittedHierarchy, restrictions]);

    return (
        <TreeSelect
            multiple
            showSearch
            treeCheckable
            dropdownStyle={{
                height: searchValue && searchValue.length < 3 ? 0 : undefined,
                maxHeight: 400,
                overflow: "auto"
            }}
            filterTreeNode={(inputValue, treeNode) => {
                if (inputValue.length < 3) return false;
                return treeNode.props.title.toLowerCase().includes(inputValue.toLowerCase());
            }}
            onChange={value => {
                onChange && onChange(value);
            }}
            onSearch={value => setSearchValue(value)}
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            maxTagCount={maxTagCount}
            value={value}
            placeholder={t("t_select_levels")}
            allowClear={allowClear}
            {...props}
        >
            {treeData}
        </TreeSelect>
    );
};

PermissionConfigurator.propTypes = {
    value: PropTypes.oneOfType([PropTypes.array, PropTypes.arrayOf(PropTypes.string)]),
    onChange: PropTypes.func,
    restrictions: PropTypes.arrayOf(PropTypes.string)
};

export default PermissionConfigurator;
