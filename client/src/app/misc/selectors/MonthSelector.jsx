import moment from "moment";
import PropTypes from "prop-types";
import { Input, DatePicker } from "antd";
import React, { useEffect, useState } from "react";

const MonthSelector = ({ defaultValue, value, onChange = () => null, disabledDate = () => null, ...props }) => {
    const [month, setMonth] = useState(value ?? defaultValue ?? moment());

    useEffect(() => setMonth(value), [value]);

    const onChangeMonth = month => {
        setMonth(month);
        onChange(month);
    };

    return (
        <Input.Group compact>
            {/* <Button
                onClick={() => onChangeWeek(moment(week).subtract(1, "week"))}
                disabled={disabledDate(moment(week).subtract(1, "week").endOf("week"))}
            >
                <FontAwesomeIcon icon={["fal", "chevron-left"]} />
            </Button> */}
            <DatePicker value={month} picker="month" format="MM / YYYY" onChange={onChangeMonth} disabledDate={disabledDate} {...props} />
            {/* <Button onClick={() => onChangeWeek(moment(week).add(1, "week"))} disabled={disabledDate(moment(week).add(1, "week").startOf("week"))}>
                <FontAwesomeIcon icon={["fal", "chevron-right"]} />
            </Button> */}
        </Input.Group>
    );
};

MonthSelector.propTypes = {
    defaultValue: PropTypes.instanceOf(moment),
    value: PropTypes.instanceOf(moment),
    onChange: PropTypes.func,
    disabledDate: PropTypes.func
};

export default MonthSelector;
