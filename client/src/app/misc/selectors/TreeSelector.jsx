import { TreeSelect } from "antd";
import PropTypes from "prop-types";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

const FIELD_NAMES = { children: "children", value: "value", label: "label" };

const TreeSelector = ({ defaultValue = [], value = [], onChange = () => null, options = [], fieldNames = FIELD_NAMES, ...props }) => {
    const [t] = useTranslation();

    const treeData = useMemo(() => {
        const formatTreeLeaf = (leaf, parent) => {
            if (leaf[fieldNames.children])
                return {
                    ...leaf,
                    label: t(leaf.label),
                    value: parent ? `${parent}_${leaf.value}` : leaf.value,
                    [fieldNames.children]: leaf[fieldNames.children].map(child => formatTreeLeaf(child, leaf.value))
                };

            return { ...leaf, label: t(leaf.label), value: parent ? `${parent}_${leaf.value}` : leaf.value };
        };

        return options.map(option => formatTreeLeaf(option));
    }, [options, fieldNames, t]);

    return (
        <TreeSelect
            treeData={treeData}
            value={value}
            defaultValue={defaultValue}
            placeholder={t("t_selector_placeholder_default")}
            onChange={value => {
                onChange(value);
                //if (value.length < 1) allowClear && onChange(value);
                //else onChange(value);
            }}
            filterTreeNode={(inputValue, treeNode) => {
                treeNode[fieldNames.label].toLowerCase().includes(inputValue.toLowerCase());
            }}
            fieldNames={fieldNames}
            style={{ width: "100%" }}
            treeCheckable
            {...props}
        />
    );
};

TreeSelector.propTypes = {
    defaultValue: PropTypes.array,
    value: PropTypes.array,
    onChange: PropTypes.func,
    options: PropTypes.array
};

export default TreeSelector;
