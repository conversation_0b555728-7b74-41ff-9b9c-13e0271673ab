import { Select } from "antd";
import PropTypes from "prop-types";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { TextField } from "misc/fields";

const DaySelector = ({ value, onChange, ...props }) => {
    const [t] = useTranslation();

    const days = useMemo(
        () => [
            { key: "weekdays", label: "t_weekdays" },
            { key: "mondays", label: "t_mondays" },
            { key: "tuesdays", label: "t_tuesdays" },
            { key: "wednesdays", label: "t_wednesdays" },
            { key: "thursdays", label: "t_thursdays" },
            { key: "fridays", label: "t_fridays" },
            { key: "saturdays", label: "t_saturdays" },
            { key: "sundays", label: "t_sundays" },
            { key: "holidays", label: "t_holidays" }
        ],
        []
    );

    return (
        <Select
            mode="multiple"
            value={value}
            onChange={onChange}
            placeholder={t("t_select_days")}
            filterOption={(inputValue, option) => t(option.label).toLowerCase().includes(inputValue.toLowerCase())}
            {...props}
        >
            {days.map(day => (
                <Select.Option key={day.key} value={day.key} label={day.label}>
                    <TextField value={day.label} />
                </Select.Option>
            ))}
        </Select>
    );
};

DaySelector.propTypes = {
    value: PropTypes.string,
    onchange: PropTypes.func
};

export default DaySelector;
