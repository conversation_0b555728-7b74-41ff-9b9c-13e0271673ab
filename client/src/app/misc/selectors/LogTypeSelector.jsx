import React from "react";
import PropTypes from "prop-types";
import { Select } from "antd";

import { TextField } from "misc/fields";

const LogTypeSelector = ({ defaultValue, value, onChange }) => {
    return (
        <Select style={{ width: 200 }} value={value} defaultValue={defaultValue} onChange={onChange}>
            <Select.Option key="technical" value="technical">
                <TextField value="t_technical" />
            </Select.Option>
            <Select.Option key="financial" value="financial">
                <TextField value="t_financial_compliant" />
            </Select.Option>
        </Select>
    );
};

LogTypeSelector.propTypes = {
    value: PropTypes.string,
    defaultValue: PropTypes.string,
    onChange: PropTypes.func
};

export default LogTypeSelector;
