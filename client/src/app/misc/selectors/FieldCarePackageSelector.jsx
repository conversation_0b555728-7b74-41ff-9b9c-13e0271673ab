import React from "react";
import PropTypes from "prop-types";
import { Select } from "antd";
import { useTranslation } from "react-i18next";
import snakeCase from "lodash/snakeCase";

import { TextField } from "misc/fields";

const FieldCarePackageSelector = ({ defaultValue, value, onChange, allowClear = true, ...props }) => {
    const [t] = useTranslation();

    const options = [];

    const handleChange = values => onChange && onChange(values);

    return (
        <Select
            mode="multiple"
            value={value}
            defaultValue={defaultValue}
            onChange={handleChange}
            placeholder={t("t_select_fpp_type")}
            style={{ width: "100%" }}
            allowClear={allowClear}
            {...props}
        >
            {options.map(item => (
                <Select.Option key={item.fieldCarePackageId} value={item.fieldCarePackage}>
                    <TextField value={`t_${snakeCase(item.fieldCarePackage)}`} />
                </Select.Option>
            ))}
        </Select>
    );
};

FieldCarePackageSelector.propTypes = {
    defaultValue: PropTypes.arrayOf(PropTypes.string),
    value: PropTypes.arrayOf(PropTypes.string),
    onChange: PropTypes.func,
    allowClear: PropTypes.bool
};

export default FieldCarePackageSelector;
