import React from "react";
import PropTypes from "prop-types";
import moment from "moment";

import { TextField } from "misc/fields";

const DurationField = ({ duration, className, ...props }) => {
    let formatted = duration;

    if (duration > 24 * 60 * 60) formatted = moment.duration(duration, "seconds").format("HH [h]");
    else if (duration > 60 * 60) formatted = moment.duration(duration, "seconds").format("HH [h] mm [min]");
    else if (duration) formatted = moment.duration(duration, "seconds").format("HH [h] mm[min] ss[sec]");

    return <TextField className={className} value={formatted} {...props} />;
};

DurationField.propTypes = {
    duration: PropTypes.number
};

export default DurationField;
