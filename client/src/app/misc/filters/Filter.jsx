import { useTranslation } from "react-i18next";
import { snakeCase, isObject } from "lodash";
import React, { useState, useMemo, useCallback } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Space, Button, Tag, Drawer, Form, Tooltip } from "antd";
import PropTypes from "prop-types";

import { TextField, RangeField } from "misc/fields";
import { useFlatHierarchy } from "misc/api/datalayer";
import { PermissionConfigurator } from "misc/configurators";
import {
    DaySelector,
    RangeSelector,
    ErrorSelector,
    PowertrainSelector,
    ManufacturerSelector,
    ConfigurationSelector,
    FieldCarePackageSelector
} from "misc/selectors";

import { stsColors } from "styles";

const Filter = ({ filter = {}, filterOptions = [], onChange }) => {
    const [t] = useTranslation();

    const { flatHierarchy: organisationalUnits } = useFlatHierarchy();

    const [drawerForm] = Form.useForm();
    const [drawerIsOpen, setDrawerIsOpen] = useState(false);

    const getFilterOptions = useCallback(() => filterOptions.map(option => (isObject(option) ? option.name : option)), [filterOptions]);

    const getFilterProps = useCallback(filterOption => filterOptions.find(option => option.name === filterOption), [filterOptions]);

    const items = useMemo(
        () => [
            {
                name: "range",
                label: "t_timeperiod",
                renderFilter: <RangeSelector showTime={false} {...(getFilterProps("range") ?? {})} />,
                renderTag: value => (
                    <RangeField value={value} format={getFilterProps("range")?.showTime === true ? "DD.MM.YY HH:mm:SS" : "DD.MM.YYYY"} />
                )
            },
            {
                name: "days",
                label: "t_days",
                renderFilter: <DaySelector allowClear maxTagCount={2} {...(getFilterProps("days") ?? {})} />,
                renderTag: value => value.map(day => t(`t_${day}`)).join(", ")
            },
            {
                name: "hierarchy",
                label: "t_select_organisational_units",
                renderFilter: <PermissionConfigurator maxTagCount={2} {...(getFilterProps("hierarchy") ?? {})} />,
                renderTag: value =>
                    organisationalUnits
                        .filter(item => value.includes(item.uuid))
                        .map(item => item.name)
                        .join(", ")
            },
            {
                name: "powertrains",
                label: "t_powertrains",
                renderFilter: <PowertrainSelector {...(getFilterProps("powertrains") ?? {})} />,
                renderTag: value => value.map(element => t(`t_${element}`)).join(", ")
            },
            {
                name: "manufacturers",
                label: "t_manufacturers",
                renderFilter: <ManufacturerSelector {...(getFilterProps("manufacturers") ?? {})} />,
                renderTag: value => value.map(element => t(`t_${element}`)).join(", ")
            },
            {
                name: "configurations",
                label: "t_configurations",
                renderFilter: <ConfigurationSelector {...(getFilterProps("configurations") ?? {})} />,
                renderTag: value => value.join(", ")
            },
            {
                name: "fieldCarePackages",
                label: "t_fpp",
                renderFilter: <FieldCarePackageSelector {...(getFilterProps("fieldCarePackages") ?? {})} />,
                renderTag: value => value.map(element => t(`t_${snakeCase(element)}`)).join(", ")
            },
            {
                name: "errors",
                label: "t_errors",
                renderFilter: <ErrorSelector />,
                renderTag: value => value.map(element => t(`t_error_${element}`)).join(", ")
            }
        ],
        [t, organisationalUnits, getFilterProps]
    );

    const activeFilter = Object.entries(filter)
        .filter(([key, value]) => getFilterOptions().includes(key) && items.some(item => item.name === key))
        .some(([key, value]) => value?.length);

    const onFinishForm = values => {
        setDrawerIsOpen(false);

        const newFilter = { ...filter, ...values };

        onChange && onChange(newFilter);
    };

    const onDeleteFilterOption = filterOption => {
        const newFilter = { ...filter, [filterOption]: [] };

        drawerForm.setFieldsValue(newFilter);

        onChange && onChange(newFilter);
    };

    return (
        <>
            <Space size={10}>
                <Tooltip
                    title={
                        <TextField
                            value={items.some(item => getFilterOptions().includes(item.name)) ? "t_filter_view" : "t_this_view_cannot_be_filtered"}
                        />
                    }
                >
                    <Button
                        type={activeFilter && "primary"}
                        onClick={() => setDrawerIsOpen(!drawerIsOpen)}
                        disabled={!items.some(item => getFilterOptions().includes(item.name))}
                    >
                        <FontAwesomeIcon icon={["far", "filter"]} />
                    </Button>
                </Tooltip>
                <Space size={2}>
                    {items
                        .filter(item => getFilterOptions().includes(item.name))
                        .map(item =>
                            filter[item.name]?.length ? (
                                <Tag
                                    key={item.name}
                                    onClose={() => onDeleteFilterOption(item.name)}
                                    onClick={() => setDrawerIsOpen(true)}
                                    color={stsColors.blue2}
                                    closable={getFilterProps(item.name)?.allowClear !== false}
                                >
                                    <TextField
                                        value={item.renderTag(filter[item.name])}
                                        style={{ color: stsColors.white1, fontWeight: 600, maxWidth: 250, cursor: "pointer" }}
                                        ellipsis
                                    />
                                </Tag>
                            ) : null
                        )}
                </Space>
            </Space>
            <Drawer title={<TextField value="t_filter" />} visible={drawerIsOpen} onClose={() => setDrawerIsOpen(false)} width={400} destroyOnClose>
                <Form form={drawerForm} initialValues={filter} layout="vertical" onFinish={onFinishForm} hideRequiredMark>
                    {items
                        .filter(item => getFilterOptions().includes(item.name))
                        .map(item => (
                            <Form.Item
                                key={item.name}
                                name={item.name}
                                label={<TextField value={item.label} />}
                                rules={[
                                    {
                                        required: getFilterProps(item.name)?.allowClear === false,
                                        message: t("t_this_field_cannot_be_empty")
                                    }
                                ]}
                            >
                                {item.renderFilter}
                            </Form.Item>
                        ))}
                    <Button type="primary" htmlType="submit">
                        <TextField value="t_apply" inline />
                    </Button>
                </Form>
            </Drawer>
        </>
    );
};

Filter.propTypes = {
    filter: PropTypes.object,
    filterOptions: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.object])),
    onChange: PropTypes.func
};

export default Filter;
