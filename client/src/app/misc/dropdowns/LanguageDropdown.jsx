import React from "react";
import { Button, Dropdown, Spin } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const languages = [
    { label: "German", key: "de" },
    { label: "English", key: "en" },
    { label: "Japanese", key: "jp" }
];

const LanguageDropdown = ({ value = "en", iconStyle, onChange, loading, ...props }) => {
    return (
        <Dropdown
            trigger="click"
            arrow={{ pointAtCenter: true }}
            menu={{
                items: languages,
                selectable: true,
                defaultSelectedKeys: ["en"],
                selectedKeys: [value],
                onClick: ({ key }) => onChange(key),
                ...props
            }}
            disabled={loading}
        >
            <Button shape="circle">
                {loading ? (
                    <Spin size="small" style={{ display: "flex", justifyContent: "center" }} />
                ) : (
                    <FontAwesomeIcon icon={["far", "globe"]} style={iconStyle} />
                )}
            </Button>
        </Dropdown>
    );
};

export default LanguageDropdown;
