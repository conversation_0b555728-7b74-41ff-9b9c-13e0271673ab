// Constants
$fontSize10: 0.714em;
$fontSize11: 0.7855em;
$fontSize12: 0.857em;
$fontSize13: 0.929em;
$fontSize14: 1em;
$fontSize15: 1.071em;
$fontSize16: 1.143em;
$fontSize17: 1.214em;
$fontSize18: 1.285em;
$fontSize19: 1.3571em;
$fontSize20: 1.428em;
$fontSize22: 1.571em;
$fontSize24: 1.714em;
$fontSize26: 1.8574em;
$fontSize28: 2em;
$fontSize30: 2.143em;
$fontSize32: 2.286em;
$fontSize34: 2.429em;
$fontSize36: 2.571em;
$fontSize38: 2.7143em;
$fontSize40: 2.857em;
$fontSize70: 5em;

// Class definitions
.FontSize10 {
    font-size: $fontSize10;
}

.FontSize11 {
    font-size: $fontSize11;
}

.FontSize12 {
    font-size: $fontSize12;
}

.FontSize14 {
    font-size: $fontSize14;
}

.FontSize15 {
    font-size: $fontSize15;
}

.FontSize16 {
    font-size: $fontSize16;
}

.FontSize17 {
    font-size: $fontSize17;
}

.FontSize18 {
    font-size: $fontSize18;
}

.FontSize19 {
    font-size: $fontSize19;
}

.FontSize20 {
    font-size: $fontSize20;
}

.FontSize22 {
    font-size: $fontSize22;
}

.FontSize24 {
    font-size: $fontSize24;
}

.FontSize26 {
    font-size: $fontSize26;
}

.FontSize28 {
    font-size: $fontSize28;
}

.FontSize30 {
    font-size: $fontSize30;
}

.FontSize32 {
    font-size: $fontSize32;
}

.FontSize34 {
    font-size: $fontSize34;
}

.FontSize36 {
    font-size: $fontSize36;
}

.FontSize38 {
    font-size: $fontSize38;
}

.FontSize40 {
    font-size: $fontSize40;
}

.FontWeight300 {
    font-weight: 300;
}

.FontWeight600 {
    font-weight: 600;
}
