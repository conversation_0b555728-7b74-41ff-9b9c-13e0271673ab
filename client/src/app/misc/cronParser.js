import * as parser from "cron-parser";

const isDaily = value => /^\d\s\d\s\*\s\*\s\*$/.test(value);
const isWeekly = value => /^\d\s\d\s\*\s(\*|[1-9]|1[0-2])\s[0-6]$/.test(value);
const isMonthly = value => /^\d\s\d\s([1-9]|[12]\d|3[0-1])\s\*\s\*$/.test(value);

export const decode = cronString => {
    if (isDaily(cronString)) return "daily";
    else if (isWeekly(cronString)) return "weekly";
    else if (isMonthly(cronString)) return "monthly";
    else return "unknown";
};

export const encode = (rythm, number) => {
    if (rythm === "daily") return "0 9 * * *";
    else if (rythm === "weekly" && number !== undefined) return `0 9 * * ${number}`;
    else if (rythm === "monthly" && number !== undefined) return `0 9 ${number} * *`;
    else return "";
};

export const next = cronString => parser.parseExpression(cronString).next();
