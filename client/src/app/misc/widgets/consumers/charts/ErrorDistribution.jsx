import React from "react";
import round from "lodash/round";
import snakeCase from "lodash/snakeCase";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";

import { <PERSON>rror<PERSON>elp } from "misc/help";
import { colorForError } from "misc/styles/Colors";
import { PieChartWidget } from "misc/widgets/consumers";
import { datalayerUrls } from "misc/urls/datalayerUrls";

const ErrorDistribution = ({ filter, vin, height }) => {
    const [t] = useTranslation();

    return (
        <PieChartWidget
            title="t_error_distribution"
            request={{
                url: datalayerUrls.vehicles.widgets.pieChart(),
                params: { type: "errorDistribution", vin: vin },
                filter: filter
            }}
            extractData={data => Object.entries(data).map(([key, value]) => ({ key: key, value: value }))}
            height={height}
            chartConfig={{
                angleField: "value",
                colorField: "key",
                color: ({ key }) => key && colorForError[key.substring(5)],
                meta: {
                    key: {
                        formatter: value => t(`t_${snakeCase(value)}`)
                    }
                },
                statistic: {
                    title: {
                        formatter: () => t("t_total")
                    },
                    content: {
                        formatter: (_, items) => items.reduce((acc, item) => acc + item.value, 0)
                    }
                },
                label: {
                    type: "outer",
                    offset: 32,
                    formatter: v => (v.percent > 0 ? `${t(`t_error_${v.key.substring(5)}`)}: ${v.value} | ${round(v.percent * 100)}%` : null)
                },
                empty: {
                    isEmpty: data => data.length < 1 || !data.some(item => item.value > 0),
                    title: "t_no_errors",
                    subTitle: "t_no_errors_existing"
                }
            }}
            help={{ renderHelp: () => <ErrorHelp /> }}
        />
    );
};

ErrorDistribution.propTypes = {
    filter: PropTypes.object,
    vin: PropTypes.string,
    height: PropTypes.number
};

export default ErrorDistribution;
