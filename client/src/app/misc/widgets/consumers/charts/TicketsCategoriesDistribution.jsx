import React from "react";
// import { Table } from "antd";
// import { colorForError } from "misc/styles/Colors";
// import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { Widget } from "misc/widgets";
import { ColumnChart } from "app/misc/charts";
import { ChartWidgetPropType } from "misc/propTypes";
import { defectCategories } from "misc/styles/Colors";

const TicketsCategoriesDistribution = ({ title, request }) => {
    const { isFetching, error, data = [] } = useQuery([request]);
    // const [t] = useTranslation();
    // const columns = [
    //     {
    //         dataIndex: "category",
    //         // render: description => description,
    //         title: t("t_reporting_category"),
    //         // width: "45%",
    //         fixed: "left"
    //     },
    //     {
    //         dataIndex: "data",
    //         // render: description => description,
    //         title: t("t_reporting_category")
    //         // width: "45%",
    //         // fixed: "left"
    //     },
    //     {
    //         dataIndex: "count",
    //         // render: description => description,
    //         title: t("t_reporting_category")
    //         // width: "45%",
    //         // fixed: "left"
    //     }
    // ];
    const chartConfig = {
        data: data,
        xField: request.params.view === "week" ? "week" : "date",
        yField: "count",
        seriesField: request.params.type === "defectCountHistogram" ? "category" : "description",
        colorField: "categoryId",
        isStack: true,
        // label: {
        //     formatter: ({ count }) => count
        //     //(request.params.view === "week" && value > 3 ? value : request.params.view === "day" ? value : null)
        // },
        legend: { position: "right", layout: "vertical", flipPage: true, padding: [0, 40, 0, 0] },
        color: Object.values(defectCategories),
        interactions: [{ type: "element-highlight-by-color" }, { type: "element-link" }],
        loading: isFetching
    };
    return (
        <Widget title={title} error={error} lastUpdate={data.lastUpdate} height={600}>
            {/* <Table dataSource={data.data} columns={columns} loading={isFetching} size="small" /> */}
            <ColumnChart chartConfig={chartConfig} />
        </Widget>
    );
};
TicketsCategoriesDistribution.propTypes = ChartWidgetPropType;

export default TicketsCategoriesDistribution;
