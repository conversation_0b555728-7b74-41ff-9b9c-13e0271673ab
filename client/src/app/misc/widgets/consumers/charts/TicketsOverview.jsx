import React from "react";
import { useTranslation } from "react-i18next";
import { Widget } from "misc/widgets";
import { ColumnChart } from "app/misc/charts";
import { ChartWidgetPropType } from "misc/propTypes";
import { useQuery } from "react-query";

const TicketsOverview = ({ title, request }) => {
    const { isFetching, isError, data = [] } = useQuery([request]);
    const [t] = useTranslation();

    const chartConfig = {
        data: data,
        xField: request.params.view === "week" ? "week" : "date",
        yField: "count",
        meta: {
            count: {
                alias: t("t_defects"),
                formatter: v => v.toFixed(2)
            }
        },
        label: { formatter: ({ count }) => count.toFixed(2) },
        annotations: [
            {
                type: "line",
                start: ["min", "median"],
                end: ["max", "median"],
                style: {
                    stroke: "black"
                }
            }
        ],
        loading: isFetching
    };
    return (
        <Widget title={title} error={isError}>
            <ColumnChart chartConfig={chartConfig} />
        </Widget>
    );
};

TicketsOverview.propTypes = ChartWidgetPropType;

export default TicketsOverview;
