import React from "react";
import { Collapse } from "antd";
import { snakeCase } from "lodash";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import moment from "moment";

import { TextField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { BarChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const ConnectivityHistogram = ({ filter, vin, view = "day", mode = "default", height, ...props }) => {
    const { t } = useTranslation();

    return (
        <BarChartWidget
            title="t_connectivity"
            height={height}
            request={{
                url: datalayerUrls.vehicles.widgets.barChart(),
                params: { type: "connectivityHistogram", vin: vin, view: view },
                filter: filter
            }}
            extractData={data => {
                if (mode === "default") {
                    return data.reduce(
                        (previous, current) => [
                            ...previous,
                            { date: current.date, week: current.week, value: current.active + current.noGps, type: "available" },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.noGsm + current.updating + current.powerSaving,
                                type: "temporaryNotAvailable"
                            },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.offline + current.potentiallyDefect + current.noBoxAssigned,
                                type: "notAvailable"
                            }
                        ],
                        []
                    );
                } else {
                    return data.reduce(
                        (previous, current) => [
                            ...previous,
                            { date: current.date, week: current.week, value: current.active, type: "active" },
                            { date: current.date, week: current.week, value: current.noGps, type: "noGps" },
                            { date: current.date, week: current.week, value: current.noGsm, type: "noGsm" },
                            { date: current.date, week: current.week, value: current.updating, type: "updating" },
                            { date: current.date, week: current.week, value: current.powerSaving, type: "powerSaving" },
                            { date: current.date, week: current.week, value: current.offline, type: "offline" },
                            { date: current.date, week: current.week, value: current.potentiallyDefect, type: "potentiallyDefect" },
                            { date: current.date, week: current.week, value: current.noBoxAssigned, type: "noBoxAssigned" }
                        ],
                        []
                    );
                }
            }}
            chartConfig={{
                isStack: true,
                isPercent: true,
                yField: "value",
                xField: view === "day" ? "date" : "week",
                yAxis: {
                    title: { text: t("t_connectivity") },
                    label: null
                },
                xAxis: {
                    title: {
                        text: t(`t_${view}_view`)
                    }
                },
                seriesField: "type",
                meta: {
                    date: { formatter: date => moment(date).format("DD.MM") },
                    week: { formatter: week => week },
                    value: { formatter: value => value },
                    type: { formatter: type => t(`t_tcu_state_${snakeCase(type)}`) }
                },
                color: ({ type }) => {
                    switch (type) {
                        case "available":
                            return stsColors.green2;
                        case "temporaryNotAvailable":
                            return stsColors.grey1;
                        case "notAvailable":
                            return stsColors.red2;
                        case "active":
                            return stsColors.green2;
                        case "noGps":
                            return stsColors.green1;
                        case "noGsm":
                            return stsColors.grey1;
                        case "updating":
                            return stsColors.grey2;
                        case "powerSaving":
                            return stsColors.grey3;
                        case "offline":
                            return stsColors.red1;
                        case "potentiallyDefect":
                            return stsColors.red2;
                        case "noBoxAssigned":
                            return stsColors.red3;
                        default:
                            return stsColors.black3;
                    }
                },
                tooltip: {
                    formatter: ({ type, value }) => {
                        return { name: t(`t_tcu_state_${snakeCase(type)}`), value: value ? `${Math.round(100 * value)} %` : t("t_no_data") };
                    }
                }
            }}
            help={{
                renderHelp: () => (
                    <Collapse defaultActiveKey={["description"]}>
                        <Collapse.Panel key="description" header={<TextField value="t_description" inline />}>
                            <TextField value="t_connectivity_histogram_x_axis_description" />
                            <TextField value="t_connectivity_histogram_y_axis_description" />
                        </Collapse.Panel>

                        {mode === "default" ? (
                            <>
                                <Collapse.Panel key="available" header={<TextField value="t_tcu_state_available" inline />}>
                                    <TextField value="t_tcu_state_available_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="temporaryNotAvailable" header={<TextField value="t_tcu_state_temporary_not_available" inline />}>
                                    <TextField value="t_tcu_state_temporary_not_available_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="notAvailable" header={<TextField value="t_tcu_state_not_available" inline />}>
                                    <TextField value="t_tcu_state_not_available_description" />
                                </Collapse.Panel>
                            </>
                        ) : (
                            <>
                                <Collapse.Panel key="active" header={<TextField value="t_tcu_state_active" inline />}>
                                    <TextField value="t_tcu_state_active_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="noGps" header={<TextField value="t_tcu_state_no_gps" inline />}>
                                    <TextField value="t_tcu_state_no_gps_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="noGsm" header={<TextField value="t_tcu_state_no_gsm" inline />}>
                                    <TextField value="t_tcu_state_no_gsm_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="updating" header={<TextField value="t_tcu_state_updating" inline />}>
                                    <TextField value="t_tcu_state_updating_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="powerSaving" header={<TextField value="t_tcu_state_power_saving" inline />}>
                                    <TextField value="t_tcu_state_power_saving_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="offline" header={<TextField value="t_tcu_state_offline" inline />}>
                                    <TextField value="t_tcu_state_offline_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="potentiallyDefect" header={<TextField value="t_tcu_state_potentially_defect" inline />}>
                                    <TextField value="t_tcu_state_potentially_defect_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="noBoxAssigned" header={<TextField value="t_tcu_state_no_box_assigned" inline />}>
                                    <TextField value="t_tcu_state_no_box_assigned_description" />
                                </Collapse.Panel>
                            </>
                        )}
                    </Collapse>
                )
            }}
            {...props}
        />
    );
};

ConnectivityHistogram.propTypes = {
    filter: PropTypes.object,
    vin: PropTypes.string,
    view: PropTypes.string,
    mode: PropTypes.string,
    height: PropTypes.number
};

export default ConnectivityHistogram;
