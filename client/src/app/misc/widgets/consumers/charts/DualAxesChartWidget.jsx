import React, { useMemo } from "react";
import { useQuery } from "react-query";
import { request } from "misc/propTypes";
import PropTypes from "prop-types";
import isFunction from "lodash/isFunction";

import { Widget } from "misc/widgets";
import { DualAxesChart } from "app/misc/charts";

const DualAxesChartWidget = ({
    title,
    request,
    extractData,
    extractLastUpdate = () => null,
    height = 500,
    chartConfig,
    isStandalone = true,
    queryProps,
    ...props
}) => {
    const { data, isLoading, isRefetching, isError } = useQuery([request], queryProps);

    const extractedData = useMemo(() => (data ? (isFunction(extractData) ? extractData(data) : data[extractData]) : [[], []]), [data, extractData]);

    const extractedLastUpdate = useMemo(
        () => 0,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [data]
    );

    return (
        <Widget title={title} height={height} isUpdating={isRefetching} isError={isError} lastUpdate={extractedLastUpdate} {...props}>
            <DualAxesChart
                chartConfig={{
                    data: extractedData,
                    height: 0.9 * (height - 100),
                    loading: isLoading,
                    ...chartConfig
                }}
            />
        </Widget>
    );
};

DualAxesChartWidget.propTypes = {
    title: PropTypes.string,
    request: request.isRequired,
    extractData: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
    extractLastUpdate: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
    height: PropTypes.number,
    chartConfig: PropTypes.object.isRequired,
    isStandalone: PropTypes.bool,
    queryProps: PropTypes.object
};

export default DualAxesChartWidget;
