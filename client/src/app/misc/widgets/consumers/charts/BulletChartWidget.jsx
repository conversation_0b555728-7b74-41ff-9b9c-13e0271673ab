//import React from "react";
// import PropTypes from "prop-types";

// import { useQuery } from "misc/hooks";
// import { Widget } from "misc/widgets";
// import { request } from "misc/propTypes";
// import { Bullet<PERSON>hart } from "misc/charts";
// import { ErrorWidget } from "misc/widgets/consumers";

const BulletChartWidget = ({ title, height = 100, queryKey, request, extractData, extractLastUpdate = () => null, chartConfig, ...props }) => {
    return "BulletChart";

    // const { data, isFetching, isError } = useQuery(queryKey, request);

    // if (isError) return <ErrorWidget height={height} direction="horizontal" />;

    // return (
    //     <Widget title={title} height={height} lastUpdate={extractLastUpdate(data)} {...props}>
    //         <BulletChart
    //             chartConfig={{
    //                 data: typeof extractData === "string" ? data[extractData] : extractData(data),
    //                 // height: 0.9 * (height - 100),
    //                 loading: isFetching,
    //                 ...chartConfig
    //             }}
    //         />
    //     </Widget>
    // );
};

// BulletChartWidget.propTypes = {
//     title: PropTypes.string,
//     height: PropTypes.number,
//     queryKey: PropTypes.any.isRequired,
//     request: request.isRequired,
//     extractData: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
//     extractLastUpdate: PropTypes.func,
//     chartConfig: PropTypes.object.isRequired
// };

export default BulletChartWidget;
