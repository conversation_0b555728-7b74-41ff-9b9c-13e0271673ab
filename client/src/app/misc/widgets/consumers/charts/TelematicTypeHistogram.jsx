import React from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import snakeCase from "lodash/snakeCase";

import { ChartWidgetPropType } from "misc/propTypes";
import { BarChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const TelematicTypeHistogram = ({ request, height }) => {
    const [t] = useTranslation();

    const view = "day";

    return (
        <BarChartWidget
            title="t_telematic_type_histogram"
            height={height}
            // queryKey="connectivityHistogram"
            request={request}
            extractData={data =>
                data
                    ? data.reduce(
                          (previous, current) => [
                              ...previous,
                              { date: current.date, week: current.week, value: current.tcuCount, category: "tcuCount" },
                              { date: current.date, week: current.week, value: current.oluCount, category: "oluCount" },
                              { date: current.date, week: current.week, value: current.otherCount, category: "otherCount" },
                              { date: current.date, week: current.week, value: current.c2cCount, category: "c2cCount" },
                              { date: current.date, week: current.week, value: current.noneCount, category: "noneCount" }
                          ],
                          []
                      )
                    : []
            }
            chartConfig={{
                isStack: true,
                yField: "value",
                xField: view === "day" ? "date" : "week",
                yAxis: {
                    title: { text: t("t_vehicle_count") },
                    label: null
                },
                xAxis: {
                    title: {
                        text: t(`t_${view}_view`)
                    }
                },
                seriesField: "category",
                meta: {
                    date: { formatter: date => moment(date).format("DD.MM.YY") },
                    week: { formatter: week => `KW ${week}` },
                    value: { formatter: value => value },
                    category: { formatter: category => t(`t_${snakeCase(category).replace("c_2_c", "c2c")}`) }
                },
                color: ({ category }) => {
                    switch (category) {
                        case "c2cCount":
                            return stsColors.yellow2;
                        case "noneCount":
                            return stsColors.red2;
                        default:
                            return stsColors.green2;
                    }
                },
                tooltip: {
                    formatter: ({ category, value }) => {
                        return { name: t(`t_${snakeCase(category).replace("c_2_c", "c2c")}`), value: value ?? t("t_no_data") };
                    }
                }
            }}
        />
    );
};

TelematicTypeHistogram.propTypes = ChartWidgetPropType;

export default TelematicTypeHistogram;
