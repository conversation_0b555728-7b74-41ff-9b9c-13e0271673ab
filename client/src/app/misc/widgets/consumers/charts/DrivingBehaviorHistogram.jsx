import React from "react";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import moment from "moment";

import { TextField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { DualAxesChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const DrivingBehaviorHistogram = ({ filter = {}, view = "day", mode = "default", vehicle, ...props }) => {
    const { t } = useTranslation();

    return (
        <DualAxesChartWidget
            title="t_driven_mileage_and_stops"
            request={{
                url: datalayerUrls.vehicles.widgets.barChart(),
                params: { type: "drivingBehaviorHistogram", vin: vehicle, view: view },
                filter: filter
            }}
            extractData={data => {
                if (mode === "default") {
                    const extractedData = data.reduce(
                        (result, { distanceTelematicControlUnit, distanceDriversLog, ...rest }) => [
                            ...result,
                            {
                                distance: distanceDriversLog ?? distanceTelematicControlUnit,
                                type: "distance",
                                ...rest
                            }
                        ],
                        []
                    );

                    return [extractedData, extractedData];
                } else {
                    const extractedData = data.reduce(
                        (result, { distanceTelematicControlUnit, distanceDriversLog, ...rest }) => [
                            ...result,
                            { distance: distanceTelematicControlUnit, type: "distance_telematic_control_unit", ...rest },
                            { distance: distanceDriversLog, type: "distance_drivers_log", ...rest }
                        ],
                        []
                    );

                    return [extractedData, extractedData];
                }
            }}
            chartConfig={{
                xField: view === "day" ? "date" : "week",
                yField: ["distance", "stops"],

                yAxis: {
                    distance: {
                        title: { text: t("t_covered_distance") }
                    },
                    stops: {
                        title: { text: t("t_count_stops") }
                    }
                },
                geometryOptions: [
                    { geometry: "column", color: [stsColors.blue, stsColors.blue1], isGroup: true, seriesField: "type" },
                    {
                        geometry: "line",
                        point: {},
                        lineStyle: { lineWidth: 3 },
                        color: stsColors.yellow1
                    }
                ],
                meta: {
                    date: {
                        formatter: date => moment(date).format("DD.MM")
                    },
                    week: {
                        formatter: week => week
                    },
                    stops: { min: 0 },
                    distance: {
                        formatter: value => `${value} km`
                    },
                    type: {
                        formatter: value => t(`t_${value}`)
                    }
                },
                tooltip: {
                    formatter: data => {
                        return {
                            name: data.type ? t(`t_${data.type}`) : t("t_stops"),
                            value: data.stops ? data.stops : data.distance ? `${data.distance} km` : t("t_no_data")
                        };
                    }
                }
            }}
            help={{
                renderHelp: () => (
                    <Collapse defaultActiveKey={["description"]}>
                        <Collapse.Panel key="description" header={<TextField value="t_description" inline />}>
                            <TextField value="t_driving_behavior_histogram_x_axis_description" />
                            <TextField value="t_driving_behavior_histogram_y_description" />
                        </Collapse.Panel>

                        {mode === "default" ? (
                            <>
                                <Collapse.Panel key="distance" header={<TextField value="t_distance" inline />}>
                                    <TextField value="t_distance_description" />
                                </Collapse.Panel>
                            </>
                        ) : (
                            <>
                                <Collapse.Panel
                                    key="distanceTelematicControlUnit"
                                    header={<TextField value="t_distance_telematic_control_unit" inline />}
                                >
                                    <TextField value="t_distance_telematic_control_unit_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="distanceDriversLog" header={<TextField value="t_distance_drivers_log" inline />}>
                                    <TextField value="t_distance_drivers_log_description" />
                                </Collapse.Panel>
                            </>
                        )}

                        <Collapse.Panel key="stops" header={<TextField value="t_stops" inline />}>
                            <TextField value="t_stops_description" />
                        </Collapse.Panel>
                    </Collapse>
                )
            }}
            {...props}
        />
    );
};

DrivingBehaviorHistogram.propTypes = {
    filter: PropTypes.object,
    view: PropTypes.string,
    mode: PropTypes.string,
    vehicle: PropTypes.string.isRequired
};

export default DrivingBehaviorHistogram;
