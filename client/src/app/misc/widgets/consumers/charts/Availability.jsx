import React, { useState, useMemo } from "react";
import { Row, Col, Divider, Space, Button, Input } from "antd";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { PropTypes } from "prop-types";
import _ from "lodash";

import { TitleField, DateField, RangeField, NumberField } from "misc/fields";
import { DualAxes<PERSON>hart, PieChart } from "app/misc/charts";
import { ConfigurationSelector } from "misc/selectors";
import { TinyStatisticsBox } from "misc/boxes";
import { datalayerUrls } from "misc/urls";
import { Widget } from "misc/widgets";
import { Empty } from "misc/empty";

import { stsColors } from "styles";

const ConfigurationsSelectorGroup = ({ value, restrictions, onChange }) => {
    const [currentConfigurations, setCurrentConfigurations] = useState(value);

    return (
        <Input.Group compact>
            <ConfigurationSelector
                value={currentConfigurations}
                onChange={setCurrentConfigurations}
                restrictions={restrictions}
                allowClear={false}
                style={{ width: 250 }}
            />
            <Button onClick={() => onChange(currentConfigurations)}>OK</Button>
        </Input.Group>
    );
};

const Availability = ({ title, filter = {}, view = "week", height = 650, onChange }) => {
    const [t] = useTranslation();

    const { data: availabilityData = [], ...availability } = useQuery([
        {
            url: datalayerUrls.fleet.widgets.barChart(),
            params: { type: "vehicleAvailability", view },
            filter
        }
    ]);

    const { data: unusableDaysData = [], ...unusableDays } = useQuery([
        {
            url: datalayerUrls.fleet.widgets.pieChart(),
            params: { type: "vehicleUnusableDays" },
            filter
        }
    ]);

    const { data: averageAvailabilityVehicleCountData = {}, ...averageAvailabilityVehicleCount } = useQuery([
        {
            url: datalayerUrls.fleet.values(),
            params: { type: "averageAvailabilityVehicleCount" },
            filter
        }
    ]);

    const { data: averageAvailabilityData = {}, ...averageAvailability } = useQuery([
        {
            url: datalayerUrls.fleet.values(),
            params: { type: "averageAvailability" },
            filter
        }
    ]);

    const { data: noAvailabilityDayCountData = {}, ...noAvailabilityDayCount } = useQuery([
        {
            url: datalayerUrls.fleet.values(),
            params: { type: "noAvailabilityDayCount" },
            filter
        }
    ]);

    // Do not remove if axes scalling will be needed in the future

    // const minVehicles = _.min(availability?.data?.reduce((result, { available, unknown }) => [...result, available + unknown], []));

    // const maxVehicles = _.max(
    //     availability?.data?.reduce((result, { available, unknown, notAvailable }) => [...result, available + unknown + notAvailable], [])
    // );

    const availabilitySeries = useMemo(
        () =>
            availabilityData.reduce(
                (result, { date, week, available, notAvailable }) => [
                    ...result,
                    { date, week, availability: (available / (available + notAvailable)) * 100 }
                ],
                []
            ),
        [availabilityData]
    );

    const vehiclesSeries = useMemo(
        () =>
            availabilityData.reduce(
                (result, { available, unknown, notAvailable, vehiclesCount, ...rest }) => [
                    ...result,
                    { type: "notAvailable", vehiclesCount: notAvailable, ...rest },
                    { type: "unknown", vehiclesCount: unknown, ...rest },
                    { type: "available", vehiclesCount: available, ...rest }
                ],
                []
            ),
        [availabilityData]
    );

    // const isUpdating = useMemo(
    //     () =>
    //         availability.isRefetching ||
    //         unusableDays.isRefetching ||
    //         averageAvailabilityVehicleCount.isRefetching ||
    //         averageAvailability.isRefetching ||
    //         noAvailabilityDayCount.isRefetching,
    //     [availability, unusableDays, averageAvailabilityVehicleCount, averageAvailability, noAvailabilityDayCount]
    // );

    const isError = useMemo(
        () =>
            availability.isError ||
            unusableDays.isError ||
            averageAvailabilityVehicleCount.isError ||
            averageAvailability.isError ||
            noAvailabilityDayCount.isError,
        [availability, unusableDays, averageAvailabilityVehicleCount, averageAvailability, noAvailabilityDayCount]
    );

    return (
        <Widget
            title={title}
            height={height}
            extra={
                filter.configurations && filter.configurations?.length > 0 ? (
                    <ConfigurationsSelectorGroup value={filter.configurations} onChange={onChange} />
                ) : (
                    <div style={{ width: 10, height: 32 }} />
                )
            }
            // isUpdating={isUpdating}
            isError={isError}
        >
            <Row gutter={[50, 10]}>
                <Col span={24}>
                    <Row gutter={[10, 10]} justify="space-between">
                        <Col flex="0 auto">
                            <TinyStatisticsBox
                                value={averageAvailabilityVehicleCountData.value}
                                title="t_vehicles"
                                subtitle="t_average"
                                range={filter.range}
                                formatValue={value => <NumberField value={value} decimals={0} />}
                                formatRange={range => <RangeField value={range} format="DD.MM.YY" />}
                            />
                        </Col>
                        <Col flex="0 auto">
                            <TinyStatisticsBox
                                value={averageAvailabilityData.value}
                                title="t_availability"
                                subtitle="t_average"
                                range={filter.range}
                                formatValue={value => <NumberField value={value} suffix="%" decimals={1} />}
                                formatRange={range => <RangeField value={range} format="DD.MM.YY" />}
                            />
                        </Col>
                        <Col flex="0 auto">
                            <TinyStatisticsBox
                                value={noAvailabilityDayCountData.value}
                                title="t_outage_days"
                                subtitle="t_total"
                                range={filter.range}
                                formatValue={value => <NumberField value={value} decimals={0} />}
                                formatRange={range => <RangeField value={range} format="DD.MM.YY" />}
                            />
                        </Col>
                    </Row>
                </Col>

                <Divider />

                {averageAvailabilityVehicleCountData.value === 0 ? (
                    <Col xs={24}>
                        <Empty />
                    </Col>
                ) : (
                    <>
                        <Col xs={24} md={12} lg={12}>
                            <Space direction="vertical" style={{ width: "100%" }}>
                                <div>
                                    <TitleField
                                        level={5}
                                        value={`${t("t_number_of_vehicles")} / ${t("t_availability")}`}
                                        style={{ textAlign: "center", marginBottom: 3 }}
                                    />
                                    {filter.range && (
                                        <RangeField value={filter.range} format="DD.MM.YY" style={{ textAlign: "center", fontSize: 11 }} />
                                    )}
                                </div>

                                <DualAxesChart
                                    chartConfig={{
                                        height: 0.5 * height,
                                        data: [vehiclesSeries, availabilitySeries],
                                        xField: view === "week" ? "week" : "date",
                                        yField: ["vehiclesCount", "availability"],
                                        yAxis: {
                                            availability: {
                                                title: { text: t("t_availability") },
                                                min: 0,
                                                max: 100
                                            },
                                            vehiclesCount: {
                                                title: { text: view === "week" ? t("t_average_number_of_vehicles") : t("t_number_of_vehicles") }
                                                // min: (minVehicles / maxVehicles) * maxVehicles - maxVehicles * 0.2,
                                                // max: maxVehicles
                                            }
                                        },
                                        appendPadding: [10, 0],
                                        geometryOptions: [
                                            {
                                                geometry: "column",
                                                isStack: true,
                                                seriesField: "type",
                                                color: [stsColors.grey1, stsColors.blue1, stsColors.blue2]
                                            },
                                            {
                                                geometry: "line",
                                                connectNulls: false,
                                                point: {},
                                                color: stsColors.yellow1
                                            }
                                        ],
                                        tooltip: {
                                            marker: "square",
                                            vehiclesCount: {
                                                formatter: v => `${v} ${t("t_vehicles")}`
                                            }
                                        },
                                        meta: {
                                            type: {
                                                formatter: value =>
                                                    ({
                                                        notAvailable: t("t_not_available"),
                                                        unknown: t("t_unknown"),
                                                        available: t("t_available")
                                                    }[value])
                                            },
                                            vehiclesCount: {
                                                alias: t("t_vehicles_count"),
                                                formatter: v => v.toFixed(0)
                                            },
                                            availability: {
                                                alias: t("t_availability"),
                                                formatter: v => `${_.round(v ? v : 0, 1)} %`
                                            }
                                        },
                                        loading: availability.isLoading
                                        // annotations: {
                                        //     vehiclesCount: availabilityData.reduce(
                                        //         (result, { week, notAvailable, unknown, available }) => [
                                        //             ...result,
                                        //             {
                                        //                 type: "text",
                                        //                 position: [week, notAvailable + unknown + available],
                                        //                 offsetY: -10,
                                        //                 content: notAvailable + unknown + available,
                                        //                 style: { textAlign: "center", fontWeight: "bold" }
                                        //             }
                                        //         ],
                                        //         []
                                        //     )
                                        // }
                                    }}
                                />
                            </Space>
                        </Col>
                        <Col xs={24} md={12} lg={12}>
                            <Space direction="vertical" style={{ width: "100%" }}>
                                <div>
                                    <TitleField
                                        level={5}
                                        value={`${t("t_defective_vehicles_by_days")}`}
                                        style={{ textAlign: "center", marginBottom: 3 }}
                                    />
                                    {filter.range && (
                                        <DateField value={filter.range[1]} format="DD.MM.YY" style={{ textAlign: "center", fontSize: 11 }} />
                                    )}
                                </div>

                                <PieChart
                                    chartConfig={{
                                        data: unusableDaysData,
                                        height: 0.5 * height,
                                        angleField: "vehiclesCount",
                                        colorField: "unusableDays",
                                        color: [stsColors.blue, stsColors.black1, stsColors.grey3, stsColors.grey2, stsColors.grey1],
                                        statistic: {
                                            title: {
                                                formatter: el => el?.unusableDays || t("t_total")
                                            },
                                            content: {
                                                formatter: (v, items) =>
                                                    v?.vehiclesCount
                                                        ? `${v.vehiclesCount}`
                                                        : `${items.reduce((value, cur) => value + cur.vehiclesCount, 0)}`
                                            }
                                        },
                                        loading: unusableDays.isLoading,
                                        legend: {
                                            marker: {
                                                symbol: "square"
                                            },
                                            position: "bottom",
                                            itemName: {
                                                formatter: value => `${value} ${t("t_days")}`
                                            }
                                        }
                                    }}
                                />
                            </Space>
                        </Col>
                    </>
                )}
            </Row>
        </Widget>
    );
};

Availability.propTypes = {
    title: PropTypes.string,
    filter: PropTypes.object,
    view: PropTypes.string,
    height: PropTypes.number,
    onChange: PropTypes.func
};

export default Availability;
