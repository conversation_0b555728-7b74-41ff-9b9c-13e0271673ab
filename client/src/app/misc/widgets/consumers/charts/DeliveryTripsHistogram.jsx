import React from "react";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";
import snakeCase from "lodash/snakeCase";
import PropTypes from "prop-types";
import moment from "moment";

import { TextField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { BarChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const DeliveryTripsHistogram = ({ filter, vin, view = "day", height, ...props }) => {
    const { t } = useTranslation();

    return (
        <BarChartWidget
            title="t_delivery_trips"
            height={height}
            request={{
                url: datalayerUrls.vehicles.widgets.barChart(),
                params: { type: "deliveryTripsHistogram", vin: vin, view: view },
                filter: filter
            }}
            extractData={data =>
                data.reduce(
                    (previous, current) => [
                        ...previous,
                        { date: current.date, week: current.week, value: current.deliveryTrips, type: "deliveryTrip" },
                        { date: current.date, week: current.week, value: current.noDeliveryTrips, type: "noDeliveryTrip" },
                        { date: current.date, week: current.week, value: current.noInformation, type: "noInformation" }
                    ],
                    []
                )
            }
            chartConfig={{
                isStack: true,
                yField: "value",
                xField: view === "day" ? "date" : "week",
                seriesField: "type",
                yAxis: {
                    title: { text: t("t_delivery_trip") },
                    label: null,
                    tickInterval: 1
                },
                xAxis: {
                    title: {
                        text: t(`t_${view}_view`)
                    }
                },
                color: ({ type }) => {
                    switch (type) {
                        case "deliveryTrip":
                            return stsColors.green2;
                        case "noDeliveryTrip":
                            return stsColors.red2;
                        default:
                            return stsColors.grey1;
                    }
                },
                meta: {
                    type: { formatter: type => t(`t_${snakeCase(type)}`) },
                    date: { formatter: date => moment(date).format("DD.MM") },
                    week: { formatter: week => week }
                },
                tooltip: {
                    formatter: ({ type, value }) => {
                        return { name: t(`t_${snakeCase(type)}`), value: value ?? t("t_no_data") };
                    }
                }
            }}
            help={{
                renderHelp: () => (
                    <Collapse defaultActiveKey={["description"]}>
                        <Collapse.Panel key="description" header={<TextField value="t_description" inline />}>
                            <TextField value="t_delivery_trips_histogram_x_axis_description" />
                            <TextField value="t_delivery_trips_histogram_y_axis_description" />
                        </Collapse.Panel>
                        <Collapse.Panel key="deliveryTrip" header={<TextField value="t_delivery_trip" inline />}>
                            <TextField value="t_delivery_trip_description" />
                        </Collapse.Panel>
                        <Collapse.Panel key="noDeliveryTrip" header={<TextField value="t_no_delivery_trip" inline />}>
                            <TextField value="t_no_delivery_trip_description" />
                        </Collapse.Panel>
                    </Collapse>
                )
            }}
            {...props}
        />
    );
};

DeliveryTripsHistogram.propTypes = {
    filter: PropTypes.object,
    vin: PropTypes.string,
    view: PropTypes.string,
    height: PropTypes.number
};

export default DeliveryTripsHistogram;
