import React from "react";
import { useTranslation } from "react-i18next";
import { Widget } from "misc/widgets";
import { DualAxesChart } from "app/misc/charts";
import { ChartWidgetPropType } from "misc/propTypes";
import { useQuery } from "react-query";

const VARIANT_COLORS = ["#155292", "#4472B5", "#6A93DA", "#8FB6FF", "#B5DAFF"];

const TicketsHistory = ({ title, request }) => {
    const { isFetching, error, data = [] } = useQuery([request]);
    const [t] = useTranslation();

    const parsedData = data
        .map(({ configuration, data }) =>
            data.map(data => ({
                configuration,
                defectTicketCount1000: (data.defectTicketCount / data.vehicleCount) * 1000,
                ...data
            }))
        )
        .flat();

    const chartConfig = {
        data: [parsedData, parsedData],
        xField: request.params.view === "week" ? "week" : "date",
        yField: ["defectTicketCount", "defectTicketCount1000"],
        meta: {
            defectTicketCount1000: {
                formatter: v => v.toFixed(2)
            }
        },
        yAxis: {
            defectTicketCount1000: {
                title: { text: `${t("t_defects")} / 1000 ${t("t_vehicles")}` }
            },
            defectTicketCount: {
                title: { text: `${t("t_defects")} ${t("t_absolute")}` }
            }
        },

        geometryOptions: [
            {
                geometry: "column",
                isGroup: true,
                seriesField: "configuration",
                colorField: "configuration",
                color: VARIANT_COLORS,
                label: {
                    position: "middle"
                }
            },
            {
                geometry: "line",
                isGroup: true,
                seriesField: "configuration",
                colorField: "configuration",
                color: VARIANT_COLORS,
                tooltip: false
            }
        ],
        legend: {
            custom: true,
            items: request.filter.configurations.map((conf, index) => ({
                name: conf,
                value: conf,
                marker: {
                    symbol: "square",
                    style: {
                        fill: VARIANT_COLORS[index]
                    }
                }
            }))
        },
        loading: isFetching
    };
    return (
        <Widget title={title} error={error}>
            <DualAxesChart chartConfig={chartConfig} />
        </Widget>
    );
};

TicketsHistory.propTypes = ChartWidgetPropType;

export default TicketsHistory;
