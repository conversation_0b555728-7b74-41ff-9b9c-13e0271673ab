import React, { useMemo } from "react";
import { useQuery } from "react-query";
import PropTypes from "prop-types";
import _ from "lodash";

import { Widget } from "misc/widgets";
import { PieChart } from "misc/charts";
import { request } from "misc/propTypes";

const PieChartWidget = ({ title, request, extractData, extractLastUpdate = () => null, height = 500, chartConfig, queryProps, ...props }) => {
    const { data, isLoading, isRefetching, isError } = useQuery([request], queryProps);

    const extractedData = useMemo(() => (data ? (_.isFunction(extractData) ? extractData(data) : data[extractData]) : []), [data, extractData]);

    const extractedLastUpdate = useMemo(
        () => (data ? (_.isFunction(extractLastUpdate) ? extractLastUpdate(data) : data[extractLastUpdate]) : null),
        [data, extractLastUpdate]
    );

    return (
        <Widget title={title} height={height} isUpdating={isRefetching} isError={isError} lastUpdate={extractedLastUpdate} {...props}>
            <PieChart
                chartConfig={{
                    data: extractedData,
                    height: 0.82 * (height - 100),
                    loading: isLoading,
                    ...chartConfig
                }}
            />
        </Widget>
    );
};

PieChartWidget.propTypes = {
    title: PropTypes.string,
    request: request.isRequired,
    extractData: PropTypes.oneOfType([PropTypes.func, PropTypes.string]).isRequired,
    extractLastUpdate: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),
    height: PropTypes.number,
    chartConfig: PropTypes.object,
    queryProps: PropTypes.object
};

export default PieChartWidget;
