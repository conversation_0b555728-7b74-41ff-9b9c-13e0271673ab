import React from "react";
import { isNil } from "lodash";
import { Collapse } from "antd";
import { snakeCase } from "lodash";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import moment from "moment";

import { TextField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { BarChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const AmbientTemperatureHistogram = ({ filter, vin, view = "day", mode = "default", ...props }) => {
    const { t } = useTranslation();

    return (
        <BarChartWidget
            title="t_ambient_temperature_histogram"
            request={{
                method: "GET",
                url: datalayerUrls.vehicles.widgets.barChart(),
                params: {
                    type: "ambientTemperatureHistogram",
                    vin: vin,
                    view: view
                },
                filter: filter
            }}
            extractData={data => {
                if (mode === "default") {
                    return data.reduce(
                        (previous, current) => [
                            ...previous,
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureChargingStatsMin ?? current.ambientTemperatureNormalizedSignalsMin,
                                type: "ambientTemperatureMin"
                            },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureChargingStatsMax ?? current.ambientTemperatureNormalizedSignalsMax,
                                type: "ambientTemperatureMax"
                            }
                        ],
                        []
                    );
                } else {
                    return data.reduce(
                        (previous, current) => [
                            ...previous,
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureChargingStatsMin,
                                type: "ambientTemperatureChargingStatsMin"
                            },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureChargingStatsMax,
                                type: "ambientTemperatureChargingStatsMax"
                            },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureNormalizedSignalsMin,
                                type: "ambientTemperatureNormalizedSignalsMin"
                            },
                            {
                                date: current.date,
                                week: current.week,
                                value: current.ambientTemperatureNormalizedSignalsMax,
                                type: "ambientTemperatureNormalizedSignalsMax"
                            }
                        ],
                        []
                    );
                }
            }}
            chartConfig={{
                xField: view === "day" ? "date" : "week",
                isGroup: true,
                yField: "value",
                seriesField: "type",
                color: ({ type }) => {
                    switch (type) {
                        case "ambientTemperatureMin":
                            return stsColors.blue1;

                        case "ambientTemperatureMax":
                            return stsColors.yellow3;

                        case "ambientTemperatureNormalizedSignalsMin":
                            return stsColors.blue1;

                        case "ambientTemperatureNormalizedSignalsMax":
                            return stsColors.yellow3;

                        case "ambientTemperatureChargingStatsMin":
                            return stsColors.blue2;

                        case "ambientTemperatureChargingStatsMax":
                            return stsColors.red1;

                        default:
                            return stsColors.black3;
                    }
                },
                yAxis: {
                    title: { text: t("t_temperature_in_degree_celsius") }
                },
                meta: {
                    date: {
                        formatter: date => moment(date).format("DD.MM")
                    },
                    week: {
                        formatter: week => week
                    },
                    value: {
                        formatter: value => `${value}° C`
                    },
                    type: {
                        formatter: value => t(`t_${snakeCase(value)}`)
                    }
                },
                tooltip: {
                    formatter: ({ type, value }) => {
                        return { name: t(`t_${snakeCase(type)}`), value: isNil(value) ? t("t_no_data") : `${value} °C` };
                    }
                },
                label: false
            }}
            help={{
                renderHelp: () => (
                    <Collapse defaultActiveKey={["description"]}>
                        <Collapse.Panel key="description" header={<TextField value="t_description" inline />}>
                            <TextField value="t_ambient_temperature_histogram_description_x_axis" />
                            <TextField value="t_ambient_temperature_histogram_description_y_axis" />
                        </Collapse.Panel>

                        {mode === "default" ? (
                            <>
                                <Collapse.Panel key="temperatureMin" header={<TextField value="t_ambient_temperature_min" inline />}>
                                    <TextField value="t_ambient_temperature_min_description" />
                                </Collapse.Panel>
                                <Collapse.Panel key="temperatureMax" header={<TextField value="t_ambient_temperature_max" inline />}>
                                    <TextField value="t_ambient_temperature_max_description" />
                                </Collapse.Panel>
                            </>
                        ) : (
                            <>
                                <Collapse.Panel
                                    key="ambientTemperatureNormalizedSignalsMin"
                                    header={<TextField value="t_ambient_temperature_normalized_signals_min" inline />}
                                >
                                    <TextField value="t_ambient_temperature_normalized_signals_min_description" />
                                </Collapse.Panel>
                                <Collapse.Panel
                                    key="ambientTemperatureNormalizedSignalsMax"
                                    header={<TextField value="t_ambient_temperature_normalized_signals_max" inline />}
                                >
                                    <TextField value="t_ambient_temperature_normalized_signals_max_description" />
                                </Collapse.Panel>
                                <Collapse.Panel
                                    key="ambientTemperatureChargingStatsMin"
                                    header={<TextField value="t_ambient_temperature_charging_stats_min" inline />}
                                >
                                    <TextField value="t_ambient_temperature_charging_stats_min_description" />
                                </Collapse.Panel>
                                <Collapse.Panel
                                    key="ambientTemperatureChargingStatsMax"
                                    header={<TextField value="t_ambient_temperature_charging_stats_max" inline />}
                                >
                                    <TextField value="t_ambient_temperature_charging_stats_max_description" />
                                </Collapse.Panel>
                            </>
                        )}
                    </Collapse>
                )
            }}
            {...props}
        />
    );
};

AmbientTemperatureHistogram.propTypes = {
    filter: PropTypes.object,
    vin: PropTypes.string,
    view: PropTypes.string,
    mode: PropTypes.string
};

export default AmbientTemperatureHistogram;
