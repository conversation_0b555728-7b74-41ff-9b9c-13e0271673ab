import React from "react";
import { useTranslation } from "react-i18next";
import snakeCase from "lodash/snakeCase";
import moment from "moment";

import { datalayerUrls } from "misc/urls";
import { BarChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const DriveReadynessHistogram = ({ filter, vin, height }) => {
    const { t } = useTranslation();

    return (
        <BarChartWidget
            title="t_drive_readyness_histogram"
            height={height}
            request={{
                url: datalayerUrls.vehicles.widgets.barChart(),
                params: { type: "driveReadynessHistogram", vin: vin },
                filter: filter
            }}
            extractData={data =>
                data.reduce(
                    (previous, current) => [
                        ...previous,
                        { date: current.date, week: current.week, value: current.canDriveCount, driveReadyness: "canDrive" },
                        { date: current.date, week: current.week, value: current.canRollCount, driveReadyness: "canRoll" },
                        { date: current.date, week: current.week, value: current.canNotRollCount, driveReadyness: "canNotRoll" }
                    ],
                    []
                )
            }
            chartConfig={{
                isStack: true,
                isPercent: true,
                yField: "value",
                yAxis: {
                    title: { text: t("t_vehicle_status") },
                    label: null
                },

                seriesField: "driveReadyness",
                meta: {
                    date: {
                        formatter: date => moment(date).format("DD.MM")
                    },
                    week: { formatter: week => `KW ${week}` },
                    driveReadyness: { formatter: driveReadyness => t(`t_${snakeCase(driveReadyness)}`) }
                },
                color: ({ driveReadyness }) => {
                    switch (driveReadyness) {
                        case "canDrive":
                            return stsColors.yellow1;
                        case "canRoll":
                            return stsColors.yellow3;
                        default:
                            return stsColors.red2;
                    }
                },
                tooltip: {
                    formatter: ({ driveReadyness, value }) => {
                        return { name: t(`t_${snakeCase(driveReadyness)}`), value: `${Math.round(100 * value) || 0} %` ?? t("t_no_data") };
                    }
                }
                // empty: {
                //     isEmpty: data =>
                //         data.length < 1 ||
                //         !data.some(element => element.canDriveCount > 0 || element.canNotRollCount > 0 || element.canRollCount > 0),
                //     title: "t_no_defect_tickets",
                //     subTitle: "t_no_defect_tickets_existing"
                // }
            }}
        />
    );
};

export default DriveReadynessHistogram;
