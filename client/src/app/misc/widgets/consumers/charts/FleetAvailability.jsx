import React from "react";
import PropTypes from "prop-types";

import { request } from "misc/propTypes";
import { BulletChartWidget } from "misc/widgets/consumers";

const FleetAvailability = ({ request, height = 180 }) => {
    return (
        <BulletChartWidget
            title="t_availability"
            height={height}
            // queryKey="fleetAvailability"
            request={request}
            extractData={data => (data ? [{ value: [data.value], ranges: [0, 100], title: "availability" }] : [])}
            chartConfig={{
                measureField: "value",
                rangeField: "ranges",
                targetField: "value"
            }}
        />
    );
};

FleetAvailability.propTypes = {
    request: request.isRequired,
    height: PropTypes.number
};

export default FleetAvailability;
