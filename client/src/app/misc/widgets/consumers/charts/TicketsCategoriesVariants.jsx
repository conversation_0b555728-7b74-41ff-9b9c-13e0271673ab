import React from "react";
import { Widget } from "misc/widgets";
import { FacetChart } from "app/misc/charts";
// import { ChartWidgetPropType } from "misc/propTypes";
import { useQuery } from "react-query";
import { defectCategories } from "misc/styles/Colors";
import { datalayerUrls } from "misc/urls";
import { getDateRanges, humanReadableDateRange } from "app/misc/helpfunctions";

const CATEGORIES = [
    // "-",
    "Motor/Antrieb",
    "Allgemeines Elektrikproblem",
    "Wartung/Verschleiß",
    "Ladestörung",
    "Heizung",
    "Parksperre/Getriebe",
    "Karosserie (inkl. Tür)",
    "Fahrwerk/Bremssystem",
    "Hochvoltbatterie",
    "Reifenpanne",
    "Unfall",
    "Keine Handlung notwendig",
    "Sonstiges",
    "UNDEFINED"
];

const parseData = variants =>
    variants
        .map(variant =>
            variant.data
                .map(range =>
                    range.data.map(data => ({
                        ...data,
                        variant: variant.variant,
                        range: range.range.join("---"),
                        workingDayCount: range.workingDayCount
                    }))
                )
                .flat()
        )
        .flat()
        .sort((a, b) => a.workingDayCount - b.workingDayCount); // sort for order of columns

const useRequest = (view, filter) =>
    useQuery([
        {
            url: datalayerUrls.fleet.widgets.barChart(),
            params: { type: "defectCountByCategoryHistogram", view },
            filter: {
                range: getDateRanges(view === "day" ? ["today", "last4Weeks", "last8Weeks"] : ["thisWeek", "previous4Weeks", "previous8Weeks"]),
                ...filter
            }
        }
    ]);

const TicketsCategoriesVariants = ({ title, view }) => {
    const requestB = useRequest(view, { configurations: ["B"] });
    const requestD = useRequest(view, { configurations: ["D"] });
    const requestE = useRequest(view, { configurations: ["E"] });

    // reorder data from backend for multi-view facet chart
    const parsedData = parseData([
        { variant: "B", data: requestB.data },
        { variant: "D", data: requestD.data },
        { variant: "E", data: requestE.data }
    ]);

    const chartConfig = {
        data: parsedData,
        loading: requestB.isFetching || requestD.isFetching || requestE.isFetching,
        height: 800,
        padding: [16, 32, 96, 16],
        type: "rect",
        fields: ["range", "variant"],
        columnTitle: {
            formatter: v => humanReadableDateRange(v, view)
        },
        meta: {
            defectCount: { sync: true },
            category: { sync: true, values: CATEGORIES }
        },
        legend: false,
        eachView: (view, f) => ({
            type: "column",
            options: {
                data: f.data,
                xField: "category",
                yField: "defectCount",
                color: Object.values(defectCategories), //({ hotlineErrorCode }) => defectCategories[hotlineErrorCode] || "grey",
                colorField: "category",
                xAxis: {
                    label: {
                        autoRotate: true,
                        autoHide: false,
                        autoEllipsis: false
                    }
                },
                label: {
                    position: "middle",
                    formatter: ({ defectCount }) => (defectCount > 3 ? defectCount : null)
                },
                seriesField: "category"
            }
        })
    };
    return (
        <Widget title={title} height={800}>
            <FacetChart chartConfig={chartConfig} />
        </Widget>
    );
};

// TicketsCategoriesVariants.propTypes = ChartWidgetPropType;

export default TicketsCategoriesVariants;
