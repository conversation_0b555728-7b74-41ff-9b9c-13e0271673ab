import React from "react";
import { useQuery } from "react-query";
import { Widget } from "misc/widgets";
import { ColumnChart } from "app/misc/charts";
import { ChartWidgetPropType } from "misc/propTypes";
import { defectCategories } from "misc/styles/Colors";

const TicketsDefectsDistribution = ({ title, request }) => {
    const { data, isFetching, error } = useQuery([request]);

    const chartConfig = {
        data: data,
        xField: "date",
        yField: "count",
        isStack: true,
        seriesField: "description",
        color: Object.values(defectCategories), //({ hotlineErrorCode }) => defectCategories[hotlineErrorCode] || "grey",
        interactions: [{ type: "element-highlight-by-color" }, { type: "element-link" }],
        legend: { position: "right", layout: "vertical", flipPage: true, padding: [0, 40, 0, 0] },
        loading: isFetching
        // xAxis: {
        //     // label: {
        //     //     formatter: v => "<b>test</b>"
        //     // },
        //     content: "<b>{value}</b>"
        // }
    };

    return (
        <Widget title={title} error={error}>
            <ColumnChart chartConfig={chartConfig} />
        </Widget>
    );
};

TicketsDefectsDistribution.propTypes = ChartWidgetPropType;

export default TicketsDefectsDistribution;
