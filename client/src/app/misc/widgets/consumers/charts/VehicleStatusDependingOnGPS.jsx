import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";

import Column from "app/misc/charts/ColumnChart";
import { useQuery } from "react-query";

import { Widget } from "misc/widgets";
import { ErrorWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";
import { Badge, Spin } from "antd";

const VehicleStatusDependingOnGPS = ({ queryKey, request, filter = {}, height = 500 }) => {
    const { t } = useTranslation();

    const {
        data: { vehiclesPosition },
        isFetching,
        isError
    } = useQuery([request], { placeholderData: { vehiclesPosition: {} } });
    // { vehiclesPosition } = { vehiclesPosition: {} }
    const data = useMemo(
        () => ({
            ...vehiclesPosition,
            data: vehiclesPosition?.data?.reduce((result, { date, noData = true, inDepot, inGarage, lastDepot, lastGarage, other }) => {
                const countItems = [noData, inDepot, inGarage, lastDepot, lastGarage, other].reduce((result, item) => result + (item ? 1 : 0), 0);

                const ratio = noData ? 0 : 5 / countItems;

                return [
                    ...result,
                    {
                        date,
                        type: "noData",
                        value: noData ? 5 : 0
                    },
                    { date, type: "inDepot", value: inDepot ? ratio : 0 },
                    { date, type: "inGarage", value: inGarage ? ratio : 0 },
                    { date, type: "lastDepot", value: lastDepot ? ratio : 0 },
                    { date, type: "lastGarage", value: lastGarage ? ratio : 0 },
                    { date, type: "otherPosition", value: other ? ratio : 0 }
                ];
            }, [])
        }),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [filter, vehiclesPosition]
    );

    if (isFetching) return <Widget height={height} loading />;

    if (isError) return <ErrorWidget height={height} />;

    return (
        <Badge.Ribbon text={t("t_no_valid_data_basis")}>
            <Spin spinning={true} indicator={<></>}>
                <Widget title="t_vehicle_status_depending_on_gps_position">
                    <Column
                        chartConfig={{
                            data: data?.data,
                            height: height - 124,
                            isStack: true,
                            yField: "value",
                            xField: "date",
                            yAxis: {
                                title: { text: t("t_vehicle_status") },
                                label: null
                            },
                            xAxis: {
                                title: {
                                    text: t(`t_${filter?.view}_view`)
                                }
                            },
                            seriesField: "type",
                            meta: {
                                value: {
                                    formatter: value => (value === 0 ? t("t_no") : t("t_yes"))
                                },
                                date: {
                                    formatter: date => {
                                        switch (filter?.view) {
                                            case "week":
                                                return `${t("t_cw")} ${moment(date).format("w")}`;
                                            default:
                                                return moment(date).format("D. MMM");
                                        }
                                    }
                                },
                                type: {
                                    formatter: value =>
                                        ({
                                            noData: t("t_no_data"),
                                            inDepot: t("t_in_depot"),
                                            inGarage: t("t_in_garage"),
                                            lastDepot: t("t_last_in_depot"),
                                            lastGarage: t("t_last_in_garage"),
                                            otherPosition: t("t_other_position")
                                        }[value])
                                }
                            },
                            color: ({ type }) =>
                                ({
                                    noData: stsColors.transparent,
                                    inDepot: stsColors.red1,
                                    inGarage: stsColors.red2,
                                    lastDepot: stsColors.blue1,
                                    lastGarage: stsColors.blue2,
                                    otherPosition: stsColors.green1
                                }[type]),
                            label: {
                                content: ""
                            },
                            interactions: [{ type: "legend-filter", enable: false }]
                        }}
                    />
                </Widget>
            </Spin>
        </Badge.Ribbon>
    );
};

export default VehicleStatusDependingOnGPS;
