import React from "react";
import { useQuery } from "react-query";
import { Table } from "antd";
import { Widget } from "misc/widgets";
import { DualAxesChart } from "app/misc/charts";
import { ChartWidgetPropType } from "misc/propTypes";
import { orgGgbColors } from "styles/colors";
import { isDay } from "misc/helpfunctions";

const DefectsByRegion = ({ title, request }) => {
    const { data = [], isFetching, error } = useQuery([request]);

    // get dates for columns and data-parse
    const dates = [...new Set(data.map(data => data.date || data.week + "-" + data.year))].sort() || [];

    // traverse data for table
    const parsedData = [
        {
            gbb: "GBB Ost"
        },
        {
            gbb: "GBB Süd"
        },
        {
            gbb: "GBB Nord"
        },
        {
            gbb: "GBB West"
        }
    ];
    data.forEach(({ org_gbb, count, ratio, ...date }) => {
        const tempData = parsedData.find(pData => pData["gbb"] === org_gbb);
        const parsedDate = date.date || date.week + "-" + date.year;
        if (tempData) {
            tempData[parsedDate] = { count, ratio };
        } else {
            parsedData.push({ org_gbb, [parsedDate]: { count, ratio } });
        }
    });

    console.log(parsedData, data);

    // dynamically generate (date) columns for table
    const dateColumns = dates.map(date => ({
        title: isDay(date) ? new Date(date).toLocaleDateString("de-DE", { day: "2-digit", month: "2-digit", year: "2-digit" }) : `KW ${date}`, // format date
        dataIndex: date,
        ellipsis: true,
        children: [
            {
                title: "Abs",
                dataIndex: date,
                render: v => (v && v.count > 0 ? v.count : "")
            },
            {
                title: "%",
                dataIndex: date,
                render: v => (v && v.ratio > 0 ? v.ratio : "")
            }
        ]
    }));

    const columns = [
        {
            title: "RGB",
            dataIndex: "gbb",
            fixed: "left",
            render: ggb => ggb?.replace("GBB", "RGB")
        },
        ...dateColumns
    ];

    const chartConfig = {
        data: [data, data],
        xField: request.params.view === "week" ? "week" : "date",
        yField: ["count", "ratio"],
        yAxis: {
            count: {
                title: { text: "Anzahl Meldungen, absolut" }
            },
            ratio: {
                title: { text: "Anzahl Meldungen, relativ" }
            }
        },
        geometryOptions: [
            {
                geometry: "column",
                isStack: true,
                seriesField: "org_gbb",
                colorField: "org_gbb",
                color: ({ org_gbb }) => orgGgbColors[org_gbb],
                label: {
                    position: "middle"
                }
            },
            {
                geometry: "line",
                isStack: true,
                seriesField: "org_gbb",
                colorField: "org_gbb",
                color: ({ org_gbb }) => {
                    console.log(org_gbb);
                    return orgGgbColors[org_gbb];
                },
                tooltip: false
            }
        ],
        // TODO fix clickable legend. from github issues: is not possible if g2.events()
        legend: {
            custom: true,
            items: ["GBB Ost", "GBB Süd", "GBB Nord", "GBB West"].map(rgb => ({
                id: rgb, //.replace("RGB", "GBB"),
                name: rgb, //.replace("GBB", "RGB"),
                value: rgb, //.replace("RGB", "GBB"),
                marker: {
                    symbol: "square",
                    style: {
                        fill: orgGgbColors[rgb]
                    }
                }
            }))
        },
        // legend: {
        //     itemValue: {
        //         formatter: (text, item, index) => {
        //             console.log(text, item, index);
        //             if (item.marker.symbol === "square") return item;
        //         }
        //     }
        // },
        interactions: [{ type: "element-highlight-by-color" }, { type: "element-link" }],
        loading: isFetching
    };

    return (
        <Widget title={title} error={error}>
            <DualAxesChart chartConfig={chartConfig} />
            <Table columns={columns} dataSource={parsedData} pagination={false} bordered size="small" rowKey="gbb" style={{ marginTop: 32 }} />
        </Widget>
    );
};

DefectsByRegion.propTypes = ChartWidgetPropType;

export default DefectsByRegion;
