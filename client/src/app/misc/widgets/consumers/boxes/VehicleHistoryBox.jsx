import React from "react";
import { Row, Col, Collapse, Descriptions, Space, Badge } from "antd";
import PropTypes from "prop-types";

import { Card } from "misc/card";
import { AsciiMath } from "misc/math";
import { TextField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { ProgressBoxWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const VehicleHistoryBox = ({ vehicle, filter = {} }) => {
    return (
        <Card height={120}>
            <Row align="middle" justify="space-between">
                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_charging"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "chargingStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sum"Tage"_"LadenOK") / (sum"Tage"_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{'`LadenOK = "Ladezustand"_"Max" ge 80%`'}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>

                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_driving"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "drivingStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sum"Tage"_"FahrenOK") / (sum"Tage"_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{'`FahrenOK = "Strecke" ge 1km`'}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>

                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_delivery"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "deliveryStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sum"Tage"_"ZustellenOK") / (sum"Tage"_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{'`Zustell\\enOK = Zustellfahrt_"Verfügbarkeitsreport"`'}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>

                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_metadata"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "metadataStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sumMe\\tadaten_"OK") / (sumMe\\tadaten_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{'`St\\an\\do\\rtOK = St\\an\\do\\rt_"Ladeleitwarte" = St\\an\\do\\rt_"FM"`'}</AsciiMath>
                                        <AsciiMath>{"`LadepunktOK = Ei\\n\\ Ladepunkt\\ ist\\ zug\\ewiesen`"}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>

                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_defects"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "ticketStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sum"Tage"_"MängelmeldungenOK") / (sum"Tage"_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{"`Mäng\\elmeldung\\enOK = Kei\\n\\e\\ Mäng\\elmeldung\\en\\ vo\\rha\\nden`"}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>

                <Col span={4}>
                    <ProgressBoxWidget
                        title="t_status_errors"
                        request={{
                            url: datalayerUrls.vehicles.values(),
                            params: { type: "errorStatus", vin: vehicle },
                            filter: filter
                        }}
                        extractData={data => data.value * 100}
                        isStandalone={false}
                        renderHelp={() => (
                            <Collapse defaultActiveKey={["scoring", "colorCode"]}>
                                <Collapse.Panel key="scoring" header={<TextField value="t_scoring" inline />}>
                                    <Space direction="vertical" size={25}>
                                        <AsciiMath>{'`Sco\\re = (sum"Tage"_"FehlerOK") / (sum"Tage"_"Gesamt")`'}</AsciiMath>
                                        <AsciiMath>{"`Fehl\\erOK = Kei\\n\\e\\ Fehl\\er\\ vo\\rha\\nden`"}</AsciiMath>
                                    </Space>
                                </Collapse.Panel>
                                <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                                    <Descriptions column={1} colon={false}>
                                        <Descriptions.Item label={<Badge color={stsColors.grey1} />}>
                                            <TextField value="t_score" suffix="= 0%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                            <TextField value="t_score" suffix="< 50%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                            <TextField value="t_score" suffix="< 80%" />
                                        </Descriptions.Item>
                                        <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                            <TextField value="t_score" suffix="&ge; 80%" />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Collapse.Panel>
                            </Collapse>
                        )}
                    />
                </Col>
            </Row>
        </Card>
    );
};

VehicleHistoryBox.propTypes = {
    vehicle: PropTypes.string.isRequired,
    filter: PropTypes.object
};

export default VehicleHistoryBox;
