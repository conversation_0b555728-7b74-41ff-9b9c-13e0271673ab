import React from "react";
import { Col, Card, Statistic } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

const StatisticBox = ({ request, value = "value", icon, ...props }) => {
    const { data, isFetching, isError, error } = useQuery([request], { placeholderData: {} });
    const [t] = useTranslation();

    return (
        <Col flex={1}>
            <Card style={{ height: "100%" }}>
                {isError ? (
                    <Statistic
                        prefix={<FontAwesomeIcon color="red" icon={["fas", "triangle-exclamation"]} />}
                        value={error.statusText}
                        title={t("t_http_error_occurred")}
                    />
                ) : (
                    <Statistic value={data[value]} loading={isFetching} prefix={icon && <FontAwesomeIcon icon={icon} />} {...props} />
                )}
            </Card>
        </Col>
    );
};

// TicketsData.propTypes = ChartWidgetPropType;
export default StatisticBox;
