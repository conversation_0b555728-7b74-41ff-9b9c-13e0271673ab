import React from "react";
import { Row, Col } from "antd";
import PropTypes from "prop-types";
import { useQuery } from "react-query";

import { Card } from "misc/card";
import { datalayerUrls } from "misc/urls";
import { OnlineStatusBadge, DataQualityBadge, LastSignOfLifeBadge } from "misc/badges";

const VehicleStatusBox = ({ vin, isStandalone = true, queryProps = {} }) => {
    const { data: onlineStatusData = {}, ...onlineStatus } = useQuery(
        [{ url: datalayerUrls.vehicles.values(), params: { type: "onlineStatus", vin } }],
        { refetchInterval: 30 * 60 * 1000, ...queryProps }
    );

    const { data: telematicControlUnitData = {}, ...telematicControlUnit } = useQuery(
        [{ url: datalayerUrls.vehicles.values(), params: { type: "telematicControlUnit", vin } }],
        { refetchInterval: 30 * 60 * 1000, ...queryProps }
    );

    const { data: lastSignOfLifeData = {}, ...lastSignOfLife } = useQuery(
        [{ url: datalayerUrls.vehicles.values(), params: { type: "lastSignOfLife", vin } }],
        { refetchInterval: 30 * 60 * 1000, ...queryProps }
    );

    const Component = () => (
        <Row align="middle" justify="space-between">
            <Col span={6}>
                <OnlineStatusBadge
                    onlineStatus={onlineStatusData.value}
                    timestamp={onlineStatusData.timestamp}
                    isLoading={onlineStatus.isLoading}
                    isError={onlineStatus.isError}
                />
            </Col>

            <Col span={6}>
                <LastSignOfLifeBadge
                    value={lastSignOfLifeData.value}
                    timestamp={lastSignOfLifeData.timestamp}
                    isLoading={lastSignOfLife.isLoading}
                    isError={lastSignOfLife.isError}
                />
            </Col>

            <Col span={6}>
                <DataQualityBadge
                    telematicControlUnit={telematicControlUnitData.value}
                    isLoading={telematicControlUnit.isLoading}
                    isError={telematicControlUnit.isError}
                />
            </Col>
        </Row>
    );

    if (isStandalone)
        return (
            <Card height={64} bodyStyle={{ padding: "20px" }}>
                <Component />
            </Card>
        );

    return <Component />;
};

VehicleStatusBox.propTypes = {
    vin: PropTypes.string.isRequired,
    isStandalone: PropTypes.bool,
    queryProps: PropTypes.object
};

export default VehicleStatusBox;
