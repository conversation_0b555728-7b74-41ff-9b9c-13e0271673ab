import React, { useMemo } from "react";
import { useQuery } from "react-query";
import { Card, Space } from "antd";
import PropTypes from "prop-types";
import _ from "lodash";

import { TooltipTag } from "misc/tags";
import { TextField } from "misc/fields";
import { request } from "misc/propTypes";

const TooltipTagWidget = ({ title, tooltip, request, extractData, setColor, isStandalone = true, queryProps, ...props }) => {
    const { data, isLoading, isRefetching, isError } = useQuery([request], queryProps);

    const extractedData = useMemo(() => (data ? (_.isFunction(extractData) ? extractData(data) : data[extractData]) : null), [data, extractData]);

    const color = useMemo(() => (data ? (_.isFunction(setColor) ? setColor(data) : color) : null), [data, setColor]);

    const Component = () => (
        <Space direction="vertical" size={7}>
            <TextField value={title} style={{ fontSize: 18 }} />
            <TooltipTag
                tooltip={<TextField value={tooltip} />}
                tag={<TextField value={extractedData} />}
                isLoading={isLoading}
                isUpdating={isRefetching}
                isError={isError}
                color={color}
                {...props}
            />
        </Space>
    );

    if (isStandalone)
        return (
            <Card>
                <Component />
            </Card>
        );

    return <Component />;
};

TooltipTagWidget.propTypes = {
    title: PropTypes.string,
    tooltip: PropTypes.string,
    request: request.isRequired,
    extractData: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
    setColor: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
    isStandalone: PropTypes.bool,
    queryProps: PropTypes.object
};

export default TooltipTagWidget;
