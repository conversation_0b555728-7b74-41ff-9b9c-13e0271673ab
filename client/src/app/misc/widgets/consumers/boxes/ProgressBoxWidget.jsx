import React, { useMemo } from "react";
import { useQuery } from "react-query";
import PropTypes from "prop-types";
import _ from "lodash";

import { Card } from "misc/card";
import { request } from "misc/propTypes";
import { ProgressBox } from "misc/boxes";

const ProgressBoxWidget = ({ title, request, extractData, isStandalone = true, queryProps, ...props }) => {
    const { data, isLoading, isRefetching, isError } = useQuery([request], queryProps);

    const extractedData = useMemo(() => (data ? (_.isFunction(extractData) ? extractData(data) : data[extractData]) : null), [data, extractData]);

    const Component = () => (
        <ProgressBox title={title} percent={extractedData} isLoading={isLoading} isUpdating={isRefetching} isError={isError} {...props} />
    );

    if (isStandalone)
        return (
            <Card>
                <Component />
            </Card>
        );

    return <Component />;
};

ProgressBoxWidget.propTypes = {
    title: PropTypes.string,
    request: request.isRequired,
    extractData: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
    isStandalone: PropTypes.bool,
    queryProps: PropTypes.object
};

export default ProgressBoxWidget;
