import React from "react";
import PropTypes from "prop-types";
import { Image } from "antd";
import { useQuery } from "react-query";
import { request } from "misc/propTypes";
import { Widget } from "misc/widgets";
import { ErrorWidget } from "misc/widgets/consumers";

const ImageWidget = ({ height, requests, src, dependencies, ...props }) => {
    const { data, isFetching, isError } = useQuery([requests], { placeholderData: {} });

    const getImagePath = data => {
        if (!data.value) return;
        let color;

        if (data.value.toLowerCase().includes("gelb")) color = "yellow";
        else if (data.value.toLowerCase().includes("orange")) color = "orange";
        else if (data.value.toLowerCase().includes("weiß")) color = "white";

        let configuration;
        if (requests.params.vin.includes("WS5B")) configuration = "work";
        else if (requests.params.vin.includes("WS5D")) configuration = "work_l";
        else if (requests.params.vin.includes("WS5E")) configuration = "work_xl";

        return color && configuration ? `/images/${configuration}_box_${color}.png` : "/images/work_box_silhouette.png";
    };

    if (isError) return <ErrorWidget height={height} {...props} />;

    return (
        <Widget height={height} {...props} loading={isFetching}>
            <Image src={getImagePath(data)} preview={false} />
        </Widget>
    );
};

ImageWidget.propTypes = {
    height: PropTypes.number,
    requests: request,
    src: PropTypes.func.isRequired
};

export default ImageWidget;
