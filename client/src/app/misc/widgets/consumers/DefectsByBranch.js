import React from "react";
import { useQuery } from "react-query";
import { Table } from "antd";
import { Widget } from "misc/widgets";
import { ChartWidgetPropType } from "misc/propTypes";
import { isDay } from "misc/helpfunctions";

const DefectsByRegion = ({ title, request }) => {
    const { data = [], isFetching, error } = useQuery([request]);

    // get dates for columns and data-parse
    const dates = data[0]?.nls[0]?.data?.map(data => data.date || data.week + "-" + data.year) || [];

    // traverse data for table
    const parsedData = data.map(({ gbb_name, nls }) => ({
        gbb_name,
        nls: nls.map(({ data, nl_name, nl_count }) => {
            let parsedDates = {};
            data.forEach(dat => {
                parsedDates = { ...parsedDates, [dat.date || dat.week + "-" + dat.year]: dat.count };
            });
            return { nl_name, nl_count, ...parsedDates };
        })
    }));

    // dynamically generate (date) columns for table
    const dateColumns = dates.map(date => ({
        title: isDay(date) ? new Date(date).toLocaleDateString("de-DE", { day: "2-digit", month: "2-digit", year: "2-digit" }) : `KW ${date}`, // format date
        dataIndex: date,
        ellipsis: true,
        render: count => (count > 0 ? count : "")
    }));

    const columns = [
        {
            title: "RGBs",
            key: "rgb",
            render: data => data.gbb_name || data.nl_name,
            fixed: "left",
            width: 256
        },
        ...dateColumns,
        {
            title: "Gesamt",
            dataIndex: "nl_count",
            render: count => (count > 0 ? count : "")
            // fixed: "right"
        }
    ];

    return (
        <Widget title={title} error={error}>
            <Table
                columns={columns}
                dataSource={parsedData}
                pagination={false}
                bordered
                expandable={{
                    childrenColumnName: "nls",
                    defaultExpandAllRows: true, //dont work
                    defaultExpandedRowKeys: ["GBB Nord", "GBB Ost", "GBB Süd", "GBB West", "Sonstige"]
                }}
                rowKey="gbb_name"
                size="small"
                loading={isFetching}
            />
        </Widget>
    );
};

DefectsByRegion.propTypes = ChartWidgetPropType;

export default DefectsByRegion;
