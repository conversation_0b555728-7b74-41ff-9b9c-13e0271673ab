import React from "react";
import { useQuery } from "react-query";
import styled from "styled-components";
import { Table } from "antd";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Widget } from "misc/widgets";
import { buildUrl } from "misc/helpfunctions";
import { clientUrls } from "misc/urls";
import { ChartWidgetPropType } from "misc/propTypes";
import { CheckAppPermissions } from "misc/authentication";

const DrilldownLink = styled(Link)({
    cursor: "pointer",
    "&:hover": {
        textDecoration: "underline"
    }
});

const DefectsByVehicles = ({ title, request }) => {
    const { data, isFetching, error } = useQuery([request]);
    const [t] = useTranslation();
    const columns = [
        {
            title: t("t_vin"),
            dataIndex: "vin",
            fixed: "left",
            render: vin => (
                <CheckAppPermissions fallback={vin} allowed={["permission_vehicles-errors-overview"]}>
                    <Link
                        to={buildUrl({
                            path: clientUrls.vehicles.errors.overview(),
                            queryParameters: { vin, range: request.filter.range }
                        })}
                    >
                        <DrilldownLink>{vin}</DrilldownLink> {/* <DrilldownLink value={vin} /> */}
                    </Link>
                </CheckAppPermissions>
            ),
            sorter: (a, b) => a.vin.localeCompare(b.vin),
            width: "25%"
        },
        {
            title: t("t_variant"),
            dataIndex: "type",
            sorter: (a, b) => a.type.localeCompare(b.type)
        },
        {
            title: t("t_count"),
            dataIndex: "count",
            sorter: (a, b) => a.count - b.count,
            defaultSortOrder: "descend"
        }
    ];

    return (
        <Widget title={title} error={error}>
            <Table
                columns={columns}
                dataSource={data}
                pagination={{
                    pageSize: 15
                }}
                size="small"
                bordered
                loading={isFetching}
                rowKey="vin"
            />
        </Widget>
    );
};

DefectsByVehicles.propTypes = ChartWidgetPropType;

export default DefectsByVehicles;
