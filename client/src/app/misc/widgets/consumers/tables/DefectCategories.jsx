import React from "react";
import { PropTypes } from "prop-types";
import { Table } from "antd";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next";
import { Widget } from "misc/widgets";
import { request } from "misc/propTypes";
import { BulletChart } from "misc/charts";
import { humanReadableDateRange } from "app/misc/helpfunctions";

const DefectCategories = ({ request, title, ...props }) => {
    const { isFetching, error, data = [] } = useQuery([request], { placeholderData: [] });
    const [t] = useTranslation();

    const columnsByRanges = request.filter.range.map(range => {
        // get dateRange object (with translation) by comparing date string from backend
        const key = range.join("---");
        return {
            title: humanReadableDateRange(key, request.params.view),
            dataIndex: key,
            sorter: (a, b) => a[key] - b[key],
            sortDirections: ["descend", "ascend"],
            width: 128,
            render: v => (v ? v.toFixed(2) : null)
        };
    });

    // flatten the data from backend, nested by range
    const parsedData = [];
    let maxDefectsCount = 0;
    const firstRange = data && data[0]?.range.join("---");
    const lastRange = data && data[data.length - 1]?.range.join("---");

    data &&
        data.forEach(({ range, data }) => {
            const key = range.join("---");
            return data.forEach(data => {
                if (data.average > maxDefectsCount) maxDefectsCount = data.average;
                const tempData = parsedData.find(pData => pData["categoryId"] === data["categoryId"]);
                if (tempData) {
                    tempData[key] = data.average;
                } else {
                    parsedData.push({ ...data, title: "", range, [key]: data.average });
                }
            });
        });

    const columns = [
        {
            render: ({ category }) => category,
            title: t("t_reporting_category"),
            width: "30%",
            fixed: "left"
        },
        ...columnsByRanges,
        {
            title: t(`${t("t_count")} ${t("t_today")} / ${t("t_averagePerDayLast8Weeks")}`),
            sorter: (a, b) => a.last8Weeks - b.last8Weeks,
            width: 256,
            render: v => {
                const chartConfig = {
                    height: 16,
                    width: 250,
                    autoFit: false,
                    label: { target: true },
                    data: [
                        {
                            ranges: [maxDefectsCount],
                            measures: [Number(v[firstRange]?.toFixed(2))],
                            target: Number(v[lastRange]?.toFixed(2))
                        }
                    ],
                    color: {
                        range: "#E8EDF3",
                        measure: "#F4664A",
                        target: "blue"
                    },
                    measureField: "measures",
                    rangeField: "ranges",
                    targetField: "target"
                };
                return <BulletChart chartConfig={chartConfig} />;
            }
        }
    ];

    return (
        <Widget
            title={title}
            error={error}
            // download={{ data: data.data }}
        >
            <Table
                dataSource={parsedData}
                loading={isFetching}
                size="small"
                columns={columns}
                rowKey="categoryId"
                style={{ minHeight: 600 }}
                {...props}
            />
        </Widget>
    );
};

DefectCategories.propTypes = {
    request: request,
    dependencies: PropTypes.array
};

export default DefectCategories;
