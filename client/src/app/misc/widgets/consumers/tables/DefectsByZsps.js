import React from "react";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next";
import { Table } from "antd";
import { Widget } from "misc/widgets";
import { ChartWidgetPropType } from "misc/propTypes";
import { isDay } from "misc/helpfunctions";

const DefectsByZsps = ({ title, request }) => {
    const { data, isFetching, error } = useQuery([request], { placeholderData: [] });
    const [t] = useTranslation();

    // get dates for columns and data-parse
    const dates = [...new Set(data.map(data => data.date || data.week + "-" + data.year))].sort() || [];

    // traverse data for table
    const parsedData = [];
    data.forEach(({ org_zsp, count, ...date }) => {
        const tempData = parsedData.find(pData => pData["org_zsp"] === org_zsp);
        const parsedDate = date.date || date.week + "-" + date.year;
        if (tempData) {
            tempData[parsedDate] = count;
        } else {
            parsedData.push({ org_zsp, [parsedDate]: count });
        }
    });

    // dynamically generate (date) columns for table
    const dateColumns = dates.map(date => ({
        title: isDay(date) ? new Date(date).toLocaleDateString("de-DE", { day: "2-digit", month: "2-digit", year: "2-digit" }) : `KW ${date}`, // format date
        dataIndex: date,
        ellipsis: true,
        render: count => (count > 0 ? count : "")
    }));

    const columns = [
        {
            title: t("t_zsp_plural") + "/" + t("t_zb_plural"),
            dataIndex: "org_zsp",
            fixed: "left",
            width: 256
        },
        ...dateColumns,
        {
            title: "Gesamt",
            key: "nl_count",
            render: ({ org_zsp, ...data }) => Object.values(data).reduce((acc, current) => acc + current, 0),
            fixed: "right",
            sorter: (org_zspA, org_zspB) => {
                const sumA = Object.values(org_zspA).reduce((acc, current) => typeof current === "number" && acc + current, 0);
                const sumB = Object.values(org_zspB).reduce((acc, current) => typeof current === "number" && acc + current, 0);
                return sumA - sumB;
            },
            defaultSortOrder: "descend"
        }
    ];

    return (
        <Widget title={title} error={error}>
            <Table
                columns={columns}
                dataSource={parsedData}
                pagination={false}
                bordered
                // summary={pageData => {
                //     console.log(pageData);
                //     pageData.reduce(({ count }, acc) => count + acc, 0);
                // }}
                size="small"
                rowKey="org_zsp"
                loading={isFetching}
            />
        </Widget>
    );
};

DefectsByZsps.propTypes = ChartWidgetPropType;

export default DefectsByZsps;
