import React from "react";
import { PropTypes } from "prop-types";
import { Table } from "antd";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next";
import { Widget } from "misc/widgets";
import { request } from "misc/propTypes";
import { BulletChart } from "misc/charts";
import { humanReadableDateRange } from "app/misc/helpfunctions";

const TicketsCategories = ({ request, title, ...props }) => {
    const { isFetching, error, data } = useQuery([request], { placeholderData: [] });
    const [t] = useTranslation();

    const columnsByRanges = request.filter.range.map(range => {
        // get dateRange object (with translation) by comparing date string from backend

        const key = range.join("---");
        return {
            title: humanReadableDateRange(key, request.params.view),
            dataIndex: key,
            sorter: (a, b) => a[key] - b[key],
            sortDirections: ["descend", "ascend"],
            width: 128,
            render: v => (v ? v.toFixed(2) : null)
        };
    });
    // flatten the data from backend, nested by range
    const parsedData = [];
    data.forEach(({ range, data }) => {
        const key = range.join("---");
        return data.forEach(data => {
            const tempData = parsedData.find(pData => pData["defectCode"] === data["defectCode"]);
            if (tempData) {
                tempData[key] = data.average;
            } else {
                parsedData.push({ ...data, range, [key]: data.average });
            }
        });
    });

    const MAX_DEFECTS_COUNT = parsedData.sort((a, b) => b.last8Weeks - a.last8Weeks)[0];

    // console.log("MAX_DEFECTS_COUNT", MAX_DEFECTS_COUNT);
    // console.log(parsedData);
    const columns = [
        {
            render: ({ category, description }) => category || description,
            title: t("t_reporting_category"),
            width: "30%",
            fixed: "left"
        },
        ...columnsByRanges,
        {
            title: t(`${t("t_count")} ${t("t_today")} / ${t("t_averagePerDayLast8Weeks")}`),
            sorter: (a, b) => a.last8Weeks - b.last8Weeks,
            width: 128,
            render: v => {
                // console.log(v);
                const chartConfig = {
                    height: 30,
                    width: 300,
                    autoFit: false,
                    data: [
                        {
                            ranges: [MAX_DEFECTS_COUNT.count],
                            measures: [v?.last8Weeks?.toFixed(2)],
                            target: v.count
                        }
                    ],
                    color: {
                        range: "#E8EDF3",
                        measure: "#F4664A",
                        target: "#F4664A"
                    },
                    measureField: "measures",
                    rangeField: "ranges",
                    targetField: "target"
                };
                return <BulletChart chartConfig={chartConfig} />;
            }
        }
    ];

    return (
        <Widget
            title={title}
            error={error}
            // download={{ data: data.data }}
        >
            <Table
                dataSource={parsedData}
                loading={isFetching}
                size="small"
                columns={columns}
                rowKey="defectCode"
                style={{ minHeight: 600 }}
                {...props}
            />
        </Widget>
    );
};

TicketsCategories.propTypes = {
    request: request,
    dependencies: PropTypes.array
};

export default TicketsCategories;
