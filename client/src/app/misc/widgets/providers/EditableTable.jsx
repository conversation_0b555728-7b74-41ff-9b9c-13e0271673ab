import React from "react";
import PropTypes from "prop-types";
// import { Table, Spin } from "antd";

import { request } from "misc/propTypes";
// import { useRequest } from "misc/hooks";
// import { Requester } from "misc/requester";
// import { TextField } from "misc/fields";
// import { Widget } from "misc/widgets";
// import { ErrorWidget } from "misc/widgets/consumers";

// TODO rr never used?

const EditableTable = ({ title, requests, value, columns, minHeight, dependencies, ...props }) => {
    console.log("edit", requests);
    // const [loading, performRequests] = useRequest();

    // const [data, setData] = useState(null);

    // const tableRef = useRef(null);

    // const requestPageData = (pagination, filter, sorter) => {
    //     return performRequests(
    //         {
    //             ...requests,
    //             params: {
    //                 ...requests.params,
    //                 filter: Object.entries(filter)
    //                     .filter(([key, values]) => values)
    //                     .map(([key, values]) => `${key}=${values.join(",")}`),
    //                 page: pagination.current,
    //                 pageSize: pagination.pageSize,
    //                 orderBy: sorter.column?.sortFields ?? [sorter.field],
    //                 order: sorter.order
    //             }
    //         },
    //         data => setData(data)
    //     );
    // };

    // const renderSpinner = () => (
    //     <Spin>
    //         <Widget style={{ minHeight: minHeight || 950 }} />
    //     </Spin>
    // );

    // const renderContent = () => {
    //     return (
    //         <Widget title={title} style={{ minHeight: minHeight || 450 }} downloadData={value(data).data} extra={props.extra}>
    //             {/* <div ref={tableRef}>
    //                 <Table
    //                     dataSource={value(data).data}
    //                     columns={columns(value(data))}
    //                     pagination={{
    //                         defaultPageSize: 20,
    //                         pageSizeOptions: [20, 50, 100, value(data).statistics.count],
    //                         total: value(data).statistics.count,
    //                         showSizeChanger: true,
    //                         showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />,
    //                         onShowSizeChange: () => window.scrollTo({ top: 0, left: tableRef.current.offsetTop, behavior: "smooth" })
    //                     }}
    //                     onChange={(pagination, sorter, filter) => requestPageData(pagination, sorter, filter)}
    //                     size="small"
    //                     loading={loading}
    //                     {...props}
    //                     style={{ background: "red" }}
    //                 />
    //             </div> */}
    //         </Widget>
    //     );
    // };

    // const renderError = error => <ErrorWidget error={error} style={{ minHeight: minHeight || 450 }} />;

    return (
        <div>null</div>
        // <Requester
        //     requests={requests}
        //     renderSpinner={renderSpinner}
        //     renderContent={renderContent}
        //     renderError={renderError}
        //     onSuccess={setData}
        //     dependencies={dependencies}
        // />
    );
};

EditableTable.propTypes = {
    title: PropTypes.string,
    requests: request.isRequired,
    value: PropTypes.func.isRequired,
    columns: PropTypes.func.isRequired,
    minHeight: PropTypes.number,
    dependencies: PropTypes.array
};

export default EditableTable;
