import React from "react";
import PropTypes from "prop-types";
import { Progress, Space, Skeleton, Tag } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { TextField, NumberField } from "misc/fields";

import { stsColors } from "styles";

const ProgressBox = ({ title, percent, isLoading, isUpdating, isError, renderHelp = () => null, width = 120, ...props }) => {
    return (
        <Space direction="vertical" size={7}>
            {title && <TextField value={title} style={{ fontSize: 16 }} help={renderHelp(percent)} />}
            {isLoading || isUpdating ? (
                <div style={{ height: 21 }}>
                    <Skeleton title={{ width: 120 }} paragraph={false} active />
                </div>
            ) : isError ? (
                <Tag icon={<FontAwesomeIcon icon={["fal", "times-circle"]} style={{ marginRight: 5 }} />} color="error" style={{ cursor: "default" }}>
                    <TextField value="t_error" inline />
                </Tag>
            ) : (
                <Progress
                    percent={percent}
                    strokeWidth={10}
                    strokeColor={percent < 50 ? stsColors.red2 : percent < 80 ? stsColors.yellow2 : stsColors.green2}
                    trailColor={stsColors.grey1}
                    style={{ width: width, ...props.style }}
                    format={percent => <NumberField value={percent} suffix="%" style={{ color: stsColors.black1 }} />}
                    {...props}
                />
            )}
        </Space>
    );
};

ProgressBox.propTypes = {
    title: PropTypes.string,
    percent: PropTypes.number,
    isLoading: PropTypes.bool,
    isUpdating: PropTypes.bool,
    isError: PropTypes.bool,
    renderHelp: PropTypes.func
};

export default ProgressBox;
