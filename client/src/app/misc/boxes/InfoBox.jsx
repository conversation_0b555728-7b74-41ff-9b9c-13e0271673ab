import React from "react";
import PropTypes from "prop-types";
import { Row, Col, Statistic } from "antd";

import { TextField } from "misc/fields";

const InfoBox = ({ data, elements }) => {
    return elements.map(row => (
        <Row gutter={[20, 40]}>
            {row.map(col => (
                <Col span={24 / row.length}>
                    <Statistic
                        title={<TextField value={col.description} />}
                        value={data[col.key] ?? "-"}
                        prefix={col.key.format?.prefix}
                        suffix={col.key.format?.suffix}
                    />
                </Col>
            ))}
        </Row>
    ));
};

InfoBox.propTypes = {
    data: PropTypes.object.isRequired,
    elements: PropTypes.arrayOf(
        PropTypes.arrayOf(
            PropTypes.shape({
                key: PropTypes.string.isRequired,
                description: PropTypes.string.isRequired,
                format: PropTypes.object
            })
        )
    ).isRequired
};

export default InfoBox;
