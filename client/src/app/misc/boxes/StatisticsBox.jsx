import React from "react";
import PropTypes from "prop-types";
import { Stati<PERSON>, Divider, Space } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moment from "moment";
import isNil from "lodash/isNil";

import { TextField, NumberField } from "misc/fields";
import { range } from "misc/propTypes";

const StatisticsBox = ({ title, data, delta, ...props }) => {
    const renderFooter = () => {
        const getIcon = () => {
            if (delta.delta >= 0) return <FontAwesomeIcon icon={["fas", "caret-up"]} />;
            return <FontAwesomeIcon icon={["fas", "caret-down"]} />;
        };

        return (
            <div>
                <Divider style={{ marginTop: 15, marginBottom: 15 }} />
                <Space direction="horizontal" size={7}>
                    <Space direction="horizontal" size={3}>
                        {getIcon()}
                        <NumberField value={delta.delta} />
                    </Space>

                    <TextField prefix="t_within" value={moment(delta.range[1]).diff(moment(delta.range[0]), "days")} suffix="t_days" />
                </Space>
            </div>
        );
    };

    return (
        <>
            <Statistic
                title={<TextField value={title} />}
                value={data}
                formatter={value => (props.formatter ? props.formatter(value) : <NumberField value={value} />)}
                {...props}
            />
            {!isNil(delta) && renderFooter()}
        </>
    );
};

StatisticsBox.propTypes = {
    title: PropTypes.string,
    data: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    delta: PropTypes.shape({
        delta: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        range: range.isRequired
    })
};

export default StatisticsBox;
