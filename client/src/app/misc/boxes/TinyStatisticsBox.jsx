import React from "react";
import { Row, Col, Space } from "antd";
import PropTypes from "prop-types";

import { TitleField, TextField } from "misc/fields";

const TinyStatisticsBox = ({ title, subtitle, value, range, formatValue = () => null, formatRange = () => null }) => {
    return (
        <Row align="middle" gutter={[20, 10]}>
            <Col flex="0 auto">
                <span style={{ fontSize: 18 }}>{formatValue(value)}</span>
            </Col>
            <Col flex="1">
                <Space direction="vertical" size={5}>
                    {range && <span style={{ fontSize: 11 }}>{formatRange(range)}</span>}
                    <div>
                        {title && <TitleField level={5} value={title} style={{ margin: 0 }} />}
                        {subtitle && <TextField value={subtitle} style={{ fontSize: 13 }} />}
                    </div>
                </Space>
            </Col>
        </Row>
    );
};

TinyStatisticsBox.propTypes = {
    title: PropTypes.string,
    subtitle: PropTypes.string,
    value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
    range: PropTypes.array,
    formatValue: PropTypes.func,
    formatRange: PropTypes.func
};

export default TinyStatisticsBox;
