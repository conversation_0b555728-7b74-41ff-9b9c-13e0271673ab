import React from "react";
import moment from "moment";
import i18n from "i18next";
import isNumber from "lodash/isNumber";

import { Tooltip, Tag } from "antd";

import { TextField, DatetimeField } from "misc/fields";

import { stsColors } from "styles/colors";

export const classifyOnlineStatus = ({ onlineStatus, timestamp }) => {
    const ts = isNumber(timestamp) ? moment.unix(timestamp) : moment(timestamp);

    return {
        data: onlineStatus ? "t_online" : "t_offline",
        color: onlineStatus ? stsColors.green2 : timestamp && moment().diff(ts, "hours") <= 72 ? stsColors.yellow2 : stsColors.red2,
        tooltip: timestamp ? ts.fromNow() : "t_never_online"
    };
};

export const classifyVehicleStatus = ({ vehicleStatus }) => {
    switch (vehicleStatus) {
        case 1:
            return {
                data: "t_vehicle_status_out_of_service",
                color: stsColors.red2,
                tooltip: i18n.t("t_vehicle_status_out_of_service_description")
            };

        case 2:
            return {
                data: "t_vehicle_status_in_production",
                color: stsColors.yellow2,
                tooltip: i18n.t("t_vehicle_status_in_production_description")
            };

        case 3:
            return {
                data: "t_vehicle_status_test_vehicle",
                color: stsColors.yellow2,
                tooltip: i18n.t("t_vehicle_status_test_vehicle_description")
            };

        case 4:
            return {
                data: "t_vehicle_status_in_depot",
                color: stsColors.green2,
                tooltip: i18n.t("t_vehicle_status_in_depot_description")
            };

        case 5:
            return {
                data: "t_vehicle_status_on_track",
                color: stsColors.green2,
                tooltip: i18n.t("t_vehicle_status_on_track_description")
            };

        case 6:
            return {
                data: "t_inactive",
                color: stsColors.yellow2,
                tooltip: i18n.t("t_vehicle_status_inactive_description")
            };

        case 7:
            return {
                data: "t_vehicle_status_wrong_depot_assignment",
                color: stsColors.yellow2,
                tooltip: i18n.t("t_vehicle_status_wrong_depot_assignment_description")
            };

        default:
            return {
                data: "t_vehicle_status_undefined",
                color: stsColors.red2,
                tooltip: i18n.t("t_vehicle_status_undefined_description")
            };
    }
};

export const classifyLastSignOfLife = ({ timestamp }) => {
    const ts = isNumber(timestamp) ? moment.unix(timestamp) : moment(timestamp);

    return {
        data: timestamp ? ts.fromNow() : "t_never",
        color:
            moment().diff(ts, "hours") <= 24 ? stsColors.green2 : timestamp && moment().diff(ts, "hours") <= 72 ? stsColors.yellow2 : stsColors.red2,
        tooltip: timestamp ? ts.format("DD.MM.YYYY HH:mm:ss") : i18n.t("t_never")
    };
};

export const classifyTelematicControlUnit = ({ telematicControlUnit }) => {
    return {
        data: telematicControlUnit ? (telematicControlUnit.includes("tcu") ? "t_tcu" : "t_c2c") : "t_no_unit",
        color: telematicControlUnit ? (telematicControlUnit.includes("tcu") ? stsColors.green2 : stsColors.yellow2) : stsColors.red2,
        tooltip: telematicControlUnit ?? i18n.t("t_no_unit")
    };
};

export const classifySubfleetStatus = subfleetStatus => {
    switch (subfleetStatus) {
        case 2:
            return (
                <Tooltip title={<TextField value="t_subfleet_status_read_only_description" />}>
                    <Tag style={{ cursor: "default" }}>
                        <TextField value="t_subfleet_status_read_only" />
                    </Tag>
                </Tooltip>
            );

        case 3:
            return (
                <Tooltip title={<TextField value="t_subfleet_status_invalid_description" />}>
                    <Tag style={{ cursor: "default" }}>
                        <TextField value="t_subfleet_status_invalid" />
                    </Tag>
                </Tooltip>
            );

        default:
            return null;
    }
};

export const classifySharedSubfleetExpiration = expiration => {
    const expired = moment(expiration).unix() - moment().unix() <= 0;

    return (
        <Tooltip title={<DatetimeField value={expiration} />}>
            <Tag color={expired ? stsColors.red2 : stsColors.green2}>
                <TextField value={expired ? "t_expired" : moment(expiration).fromNow()} />
            </Tag>
        </Tooltip>
    );
};
