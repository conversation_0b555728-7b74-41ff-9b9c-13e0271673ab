import React, { useMemo } from "react";
import { Descriptions, Badge, Collapse } from "antd";
import moment from "moment";

import { TextField } from "misc/fields";
import { BadgeElement } from "misc/badges";

import { stsColors } from "styles";

export const OnlineStatusBadge = ({ onlineStatus, timestamp, ...props }) => {
    const status = useMemo(() => {
        if (onlineStatus) return ["t_online", stsColors.green2, "processing"];

        if (moment().diff(timestamp, "hours") <= 24) return ["t_offline", stsColors.yellow2, null];

        return ["t_offline", stsColors.red2, null];
    }, [onlineStatus, timestamp]);

    return (
        <BadgeElement
            title="t_online_status"
            tooltip={() => <TextField value={status[0]} suffix={timestamp ? moment(timestamp).fromNow() : null} />}
            color={status[1]}
            status={status[2]}
            {...props}
        />
    );
};

// TODO: Own endpoint for data quality

export const DataQualityBadge = ({ telematicControlUnit, timestamp, onClick, children, ...props }) => {
    const status = useMemo(() => {
        if (!telematicControlUnit) return ["t_data_quality_no_data", stsColors.red2];

        if (telematicControlUnit.includes("c2c")) return ["t_data_quality_restricted", stsColors.yellow2];

        return ["t_data_quality_good", stsColors.green2];
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [telematicControlUnit, timestamp]);

    return (
        <BadgeElement
            title="t_data_quality"
            tooltip={status[0]}
            color={status[1]}
            renderHelp={() => (
                <Collapse defaultActiveKey={["colorCode"]}>
                    <Collapse.Panel key="colorCode" header={<TextField value="t_color_code" inline />}>
                        <Descriptions column={1} colon={false} labelStyle={{ fontWeight: "bold" }}>
                            <Descriptions.Item label={<Badge color={stsColors.green2} />}>
                                <TextField value="t_data_quality_good_description" />
                            </Descriptions.Item>
                            <Descriptions.Item label={<Badge color={stsColors.yellow2} />}>
                                <TextField value="t_data_quality_restricted_description" />
                            </Descriptions.Item>
                            <Descriptions.Item label={<Badge color={stsColors.red2} />}>
                                <TextField value="t_data_quality_no_data_description" />
                            </Descriptions.Item>
                        </Descriptions>
                    </Collapse.Panel>
                </Collapse>
            )}
            {...props}
        />
    );
};

export const LastSignOfLifeBadge = ({ value, timestamp, ...props }) => {
    const status = useMemo(() => {
        if (!timestamp) return ["t_last_of_life_never", stsColors.red2];

        if (moment().diff(timestamp, "hours") <= 1) return [`${value}, ${moment(timestamp).fromNow()}`, stsColors.green2];

        if (moment().diff(timestamp, "hours") <= 24) return [`${value}, ${moment(timestamp).fromNow()}`, stsColors.yellow1];

        return [`${value}, ${moment(timestamp).fromNow()}`, stsColors.red2];
    }, [value, timestamp]);

    return <BadgeElement title="t_last_sign_of_life" tooltip={status[0]} color={status[1]} {...props} />;
};
