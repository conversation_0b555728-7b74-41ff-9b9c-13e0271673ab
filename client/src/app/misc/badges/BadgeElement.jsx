import React from "react";
import PropTypes from "prop-types";
import { isFunction } from "lodash";
import { Skeleton, Space, Tooltip, Badge, Tag } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { TextField } from "misc/fields";

const BadgeElement = ({ title, tooltip, color, isLoading, isError, renderHelp = () => null, ...props }) => {
    if (isLoading)
        return (
            <div style={{ height: 24 }}>
                <Skeleton title={{ width: 120, height: 24 }} paragraph={false} active />
            </div>
        );

    if (isError)
        return (
            <Space>
                <Tag icon={<FontAwesomeIcon icon={["fal", "times-circle"]} style={{ marginRight: 5 }} />} color="error" style={{ cursor: "default" }}>
                    <TextField value="t_error" inline />
                </Tag>
                <TextField value={title} style={{ fontSize: 16 }} />
            </Space>
        );

    return (
        <Tooltip title={isFunction(tooltip) ? tooltip() : <TextField value={tooltip} />}>
            <Space style={{ cursor: "default" }}>
                <Badge color={color} {...props} />
                <TextField value={title} style={{ fontSize: 16 }} help={renderHelp()} />
            </Space>
        </Tooltip>
    );
};

BadgeElement.propTypes = {
    title: PropTypes.string,
    tooltip: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
    color: PropTypes.string,
    isLoading: PropTypes.bool,
    isError: PropTypes.bool,
    renderHelp: PropTypes.func
};

export default BadgeElement;
