import React from "react";
import { <PERSON>lapse, Space } from "antd";

import { TextField, TitleField } from "misc/fields";

const ErrorHelp = () => {
    return (
        <Collapse>
            <Collapse.Panel header={<TextField value="t_error_categories_description" inline />}>
                <Space direction="vertical" size={20}>
                    <div>
                        <TitleField value="t_error_1" level={5} />
                        <TextField value="t_error_1_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_2" level={5} />
                        <TextField value="t_error_2_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_3" level={5} />
                        <TextField value="t_error_3_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_4" level={5} />
                        <TextField value="t_error_4_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_5" level={5} />
                        <TextField value="t_error_5_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_6" level={5} />
                        <TextField value="t_error_6_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_101" level={5} />
                        <TextField value="t_error_101_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_102" level={5} />
                        <TextField value="t_error_102_description" />
                    </div>
                    <div>
                        <TitleField value="t_error_103" level={5} />
                        <TextField value="t_error_103_description" />
                    </div>
                </Space>
            </Collapse.Panel>
        </Collapse>
    );
};

export default ErrorHelp;
