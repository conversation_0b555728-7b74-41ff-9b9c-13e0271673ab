import { Menu } from "antd";
import React, { useMemo } from "react";
import { Link } from "react-router-dom";
import { useLocation } from "react-router";
import PropTypes from "prop-types";

import { TextField } from "misc/fields";
import { usePermissions } from "misc/api/keycloak";

const NavigationTopBar = ({ items, ...props }) => {
    const { permissions } = usePermissions();

    const location = useLocation();

    const selectedKeys = useMemo(() => items.find(item => item.url === location.pathname)?.key, [location, items]);

    return (
        <Menu selectedKeys={selectedKeys} mode="horizontal" {...props}>
            {items
                .filter(item => (!item.allowed ? true : item.allowed.every(permission => permissions.includes(permission))))
                .map(item => (
                    <Menu.Item key={item.key}>
                        <Link to={item.url}>
                            <TextField value={item.title} />
                        </Link>
                    </Menu.Item>
                ))}
        </Menu>
    );
};

NavigationTopBar.propTypes = {
    items: PropTypes.arrayOf(
        PropTypes.shape({
            key: PropTypes.string.isRequired,
            url: PropTypes.string.isRequired,
            title: PropTypes.string,
            allowed: PropTypes.arrayOf(PropTypes.string)
        })
    ).isRequired
};

export default NavigationTopBar;
