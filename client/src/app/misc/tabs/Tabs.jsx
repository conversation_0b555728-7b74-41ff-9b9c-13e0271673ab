import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { useHistory, useLocation } from "react-router";
import { Tabs as AntdTabs } from "antd";
import { Route, Redirect, Switch } from "react-router-dom";

import { TextField } from "misc/fields";
import { CheckAppPermissions } from "misc/authentication";

const Tabs = ({ tabs }) => {
    const location = useLocation();
    const history = useHistory();

    const defaultActiveKey = useMemo(() => tabs.find(tab => tab.url === location.pathname)?.key, [tabs, location]);

    return (
        <>
            <AntdTabs defaultActiveKey={defaultActiveKey} onChange={activeKey => history.push(tabs.find(tab => tab.key === activeKey).url)}>
                {tabs.map(tab => (
                    <AntdTabs.TabPane
                        key={tab.key}
                        tab={
                            tab.allowed ? (
                                <CheckAppPermissions allowed={tab.allowed}>
                                    {tab.renderTitle ? tab.renderTitle(tab.title) : <TextField value={tab.title} />}
                                </CheckAppPermissions>
                            ) : (
                                <>{tab.renderTitle ? tab.renderTitle(tab.title) : <TextField value={tab.title} />}</>
                            )
                        }
                    />
                ))}
            </AntdTabs>

            <Switch>
                {tabs.map(tab => (
                    <Route key={`${tab.key}-route`} exact path={tab.url}>
                        {tab.render()}
                    </Route>
                ))}

                <Redirect to={tabs[0].url} />
            </Switch>
        </>
    );
};

Tabs.propTypes = {
    tabs: PropTypes.arrayOf(
        PropTypes.shape({
            key: PropTypes.string.isRequired,
            title: PropTypes.string.isRequired,
            renderTitle: PropTypes.func,
            url: PropTypes.string.isRequired,
            render: PropTypes.func.isRequired,
            allowed: PropTypes.arrayOf(PropTypes.string)
        })
    ).isRequired
};

export default Tabs;
