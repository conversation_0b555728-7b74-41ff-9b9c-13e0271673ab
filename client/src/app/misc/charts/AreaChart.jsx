import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Area } from "@ant-design/charts";

import defaultChartConfig from "./config";

const AreaChart = ({ chartConfig }) => {
    return <Area {...merge({}, defaultChartConfig, chartConfig)} />;
};

AreaChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default AreaChart;
