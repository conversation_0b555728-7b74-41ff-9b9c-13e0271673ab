import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Histogram } from "@ant-design/plots";

import { Empty } from "misc/empty";

import { defaultColumnConfig } from "./config";

const HistogramChart = ({ chartConfig }) => {
    if (!chartConfig.loading && (chartConfig.data.length < 1 || chartConfig.empty?.isEmpty(chartConfig.data)))
        return <Empty title={chartConfig.empty?.title} subTitle={chartConfig.empty?.subTitle} height={chartConfig.height} />;

    return <Histogram {...merge({}, defaultColumnConfig, chartConfig)} />;
};

HistogramChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default HistogramChart;
