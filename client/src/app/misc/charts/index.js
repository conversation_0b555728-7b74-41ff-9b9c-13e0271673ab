import { G2 } from "@ant-design/charts";
export { default as TimeseriesChartWrapper } from "./timeseries/TimeseriesChartWrapper";
export { default as TimeseriesLineChartElement } from "./timeseries/TimeseriesLineChartElement";
// export { default as TimeseriesBarChartElement } from "./timeseries/TimeseriesBarChartElement";
export { default as Bull<PERSON><PERSON><PERSON> } from "./BulletChart";
export { default as DualA<PERSON><PERSON><PERSON> } from "./DualAxesChart";
export { default as Pie<PERSON><PERSON> } from "./PieChart";
export { default as Line<PERSON><PERSON> } from "./LineChart";
export { default as <PERSON><PERSON><PERSON> } from "./BarChart";
export { default as Column<PERSON>hart } from "./ColumnChart";
export { default as AreaChart } from "./AreaChart";
export { default as Histo<PERSON><PERSON><PERSON> } from "./HistogramChart";
export { default as Facet<PERSON>hart } from "./FacetChart";

// register global hover for bar charts
G2.registerInteraction("element-link", {
    start: [
        {
            trigger: "interval:mouseenter",
            action: "element-link-by-color:link"
        }
    ],
    end: [
        {
            trigger: "interval:mouseleave",
            action: "element-link-by-color:unlink"
        }
    ]
});
