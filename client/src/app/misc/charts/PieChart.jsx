import React from "react";
import PropTypes from "prop-types";
import { Pie } from "@ant-design/charts";

import { Empty } from "misc/empty";

import { defaultPieChartConfig } from "./config";

const PieChart = ({ chartConfig }) => {
    if (!chartConfig.loading && (chartConfig.data.length < 1 || chartConfig.empty?.isEmpty(chartConfig.data)))
        return <Empty title={chartConfig.empty?.title} subTitle={chartConfig.empty?.subTitle} height={chartConfig.height} />;

    return <Pie {...{ ...defaultPieChartConfig, ...chartConfig }} />;
};

PieChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default PieChart;
