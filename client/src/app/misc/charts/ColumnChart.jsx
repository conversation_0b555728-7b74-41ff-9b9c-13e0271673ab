import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Column } from "@ant-design/charts";

import { Empty } from "misc/empty";

import { defaultColumnConfig } from "./config";

const ColumnChart = ({ chartConfig }) => {
    if (!chartConfig.loading && (chartConfig.data.length < 1 || chartConfig.empty?.isEmpty(chartConfig.data)))
        return <Empty title={chartConfig.empty?.title} subTitle={chartConfig.empty?.subTitle} height={chartConfig.height} />;

    return <Column {...merge({}, defaultColumnConfig, chartConfig)} />;
};

ColumnChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default ColumnChart;
