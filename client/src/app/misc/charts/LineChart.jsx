import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Line } from "@ant-design/charts";

import { defaultLineConfig } from "./config";

const LineChart = ({ chartConfig }) => {
    return <Line {...merge({}, defaultLineConfig, chartConfig)} />;
};

LineChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default LineChart;
