import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { DualAxes } from "@ant-design/charts";

import { Empty } from "misc/empty";

import { defaultDualAxesConfig } from "./config";

const DualAxesChart = ({ chartConfig }) => {
    if (!chartConfig.loading && (!chartConfig.data.some(element => element.length > 0) || chartConfig.empty?.isEmpty(chartConfig.data)))
        return <Empty title={chartConfig.empty?.title} subTitle={chartConfig.empty?.subTitle} height={chartConfig.height} />;

    return <DualAxes {...merge({}, defaultDualAxesConfig, chartConfig)} />;
};

DualAxesChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.arrayOf(PropTypes.array).isRequired
    }).isRequired
};

export default DualAxesChart;
