import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Bar } from "@ant-design/charts";

import { defaultBarConfig } from "./config";

const BarChart = ({ chartConfig }) => {
    return <Bar {...merge({}, defaultBarConfig, chartConfig)} />;
};

BarChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default BarChart;
