import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { TimeSeries, TimeRangeEvent, TimeRange } from "pondjs";
import { ChartRow, Charts, EventChart, ValueAxis } from "react-timeseries-charts";

const TimeseriesBlockChartElement = ({ data, value, details, visible }) => {
    const events = data.map(
        ({ timestampStart, timestampEnd, ...data }) => new TimeRangeEvent(new TimeRange(moment.unix(timestampStart), moment.unix(timestampEnd)), data)
    );

    const series = new TimeSeries({ name: "outages", events });

    const styleEvents = (event, state) => ({
        fill: event.get("color")
    });

    return (
        <ChartRow height={60} visible={visible ?? true} axisMargin={25}>
            <Charts>
                <EventChart axis="axis" series={series} style={styleEvents} hoverMarkerWidth={0} size={10} />
            </Charts>

            <ValueAxis id="axis" value={value} detail={details} width={220} height={25} min={0} max={1} />
        </ChartRow>
    );
};

TimeseriesBlockChartElement.propTypes = {
    data: PropTypes.oneOfType([PropTypes.array, PropTypes.arrayOf(PropTypes.objects)]).isRequired,
    value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    details: PropTypes.string,
    visible: PropTypes.bool
};

export default TimeseriesBlockChartElement;
