import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Facet } from "@ant-design/charts";

import defaultChartConfig from "./config";

const FacetChart = ({ chartConfig }) => {
    return <Facet {...merge({}, defaultChartConfig, chartConfig)} />;
};

FacetChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default FacetChart;
