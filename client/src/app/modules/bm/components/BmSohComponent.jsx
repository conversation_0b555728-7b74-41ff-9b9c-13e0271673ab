import React, { useMemo, useState } from "react";
import { Row, Col, Space, Divider, Typography, Popover, List, Statistic, Table as AntTable } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { clientUrls } from "misc/urls";
import { useSubfleets } from "misc/api/fleetconfiguration";
import { TextField, NumberField, DatetimeField } from "misc/fields";
import { Column<PERSON>hart, LineChart } from "misc/charts";
import { Table } from "misc/tables";
import { Widget } from "misc/widgets";
import { BadgeElement } from "misc/badges";
import { StatisticsBox } from "misc/boxes";
import { RangeSelector } from "misc/selectors";

import SideHeader from "navigation/SideHeader";
import ModuleSettings from "modules/ModuleSettings";
import { TABLE_VIEW_ITEMS } from "modules/ModuleSettings";

import { stsColors } from "styles";

// Import mock data
import mockSohData from "./data.json";

const { Text } = Typography;

const SOH_RANGES = [
    { min: 90, max: 100, label: "90-100%", color: "#52c41a", level: "excellent" },
    { min: 80, max: 89, label: "80-89%", color: "#faad14", level: "good" },
    { min: 70, max: 79, label: "70-79%", color: "#fa8c16", level: "warning" },
    { min: 0, max: 69, label: "<70%", color: "#f5222d", level: "critical" }
];

const CheckedIcon = ({ checked, colorChecked = stsColors.green2, colorUnchecked = stsColors.red1, colorWarning = stsColors.yellow2 }) => {
    if (checked === null) return <TextField />;

    if (checked === "warning") {
        return <FontAwesomeIcon icon={["fas", "triangle-exclamation"]} color={colorWarning} />;
    }

    return checked ? (
        <FontAwesomeIcon icon={["fas", "check"]} color={colorChecked} />
    ) : (
        <FontAwesomeIcon icon={["fas", "xmark"]} color={colorUnchecked} />
    );
};

const EvaluationPopover = ({ title = "t_battery_status_evaluation", evaluations, children }) => {
    const data = evaluations.filter(evaluation => !evaluation?.hidden);

    return (
        <Popover
            overlayInnerStyle={{ padding: "0 -16px", minWidth: "400px" }}
            title={<TextField value={title} />}
            placement="topLeft"
            trigger="click"
            content={
                <div>
                    <List
                        size="small"
                        split={false}
                        dataSource={data}
                        renderItem={item => (
                            <List.Item style={{ padding: "2px 0" }}>
                                <List.Item.Meta avatar={<CheckedIcon checked={item.status} />} title={item.text} />
                            </List.Item>
                        )}
                    />
                </div>
            }
        >
            <div style={{ cursor: "pointer" }}>{children}</div>
        </Popover>
    );
};

const BmSohComponent = () => {
    const { t } = useTranslation();
    const { subfleets } = useSubfleets({ suspense: true });

    // State for date range selector
    const [selectedRange, setSelectedRange] = useState([dayjs().subtract(1, "month").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);

    const { params, setParams } = useParams({
        options: [{ name: "fleet", persist: "global", allowed: value => subfleets.find(fleet => fleet.subfleetId === value) }]
    });

    const fleet = useMemo(() => subfleets.find(fleet => fleet.subfleetId === params.fleet), [params.fleet, subfleets]);

    // Process SOH data for histogram
    const sohHistogramData = useMemo(() => {
        const sohValues = mockSohData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);

        // Create bins with 5% intervals
        const binSize = 5;
        const startRange = Math.floor(minSoh / binSize) * binSize;
        const endRange = Math.ceil(maxSoh / binSize) * binSize;

        const bins = [];
        for (let i = startRange; i <= endRange; i += binSize) {
            bins.push({
                range: `${i}-${i + binSize - 1}%`,
                minValue: i,
                maxValue: i + binSize - 1,
                count: 0
            });
        }

        // Count batteries in each bin
        sohValues.forEach(soh => {
            const binIndex = Math.floor((soh - startRange) / binSize);
            if (bins[binIndex]) {
                bins[binIndex].count++;
            }
        });

        return bins.filter(bin => bin.count > 0); // Only show bins with data
    }, []);

    // Calculate actual SOH statistics from data
    const sohStatistics = useMemo(() => {
        const sohValues = mockSohData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);
        const avgSoh = sohValues.reduce((sum, soh) => sum + soh, 0) / sohValues.length;

        return {
            minimum: minSoh,
            average: avgSoh,
            maximum: maxSoh
        };
    }, []);

    // Custom range options for the selector
    const customRanges = useMemo(
        () => [
            { label: "t_range_last_week", value: [dayjs().subtract(1, "week"), dayjs()] },
            { label: "t_range_last_two_weeks", value: [dayjs().subtract(2, "weeks"), dayjs()] },
            { label: "t_range_last_month", value: [dayjs().subtract(1, "month"), dayjs()] }
        ],
        []
    );

    const exportOptions = useMemo(
        () => ({
            label: "t_soh_overview",
            columns: [
                { value: "batteryId", label: "t_battery_id" },
                { value: "currentSoh", label: "t_soh" },
                { value: "degradationRate", label: "t_degradation_rate" },
                { value: "currentVin", label: "t_current_vehicle" },
                { value: "daysSinceLastMeasurement", label: "t_days_since_measurement" },
                { value: "predictedReplacementDate", label: "t_predicted_replacement" },
                { value: "actionRequired", label: "t_action_required" },
                { value: "nominalCapacity", label: "t_nominal_capacity" },
                { value: "totalThroughput", label: "t_total_throughput" },
                { value: "batteryAge", label: "t_battery_age" }
            ],
            request: {
                method: "GET",
                url: "/mock/soh-export", // Mock endpoint since we're using local data
                filter: {
                    hierarchy: fleet?.organisationalUnits,
                    vehicles: fleet?.vehicles,
                    range: selectedRange
                }
            }
        }),
        [fleet, selectedRange]
    );

    const sohTableColumns = [
        {
            title: <TextField value="t_battery_id" />,
            dataIndex: "batteryId",
            key: "batteryId",
            render: text => <a href={`#battery/${text}`}>{text}</a>
        },
        {
            title: <TextField value="t_vin" />,
            dataIndex: "currentVin",
            key: "currentVin",
            render: vin => vin || <TextField value="t_not_assigned" />
        },
        {
            title: <TextField value="t_soh" />,
            dataIndex: "currentSoh",
            key: "currentSoh",
            sorter: (a, b) => a.currentSoh - b.currentSoh,
            render: value => <NumberField value={value} suffix="%" decimals={1} />
        },
        {
            title: <TextField value="t_battery_status" />,
            dataIndex: "currentSoh",
            key: "batteryStatus",
            filters: [
                { text: "Critical", value: "critical" },
                { text: "Warning", value: "warning" },
                { text: "Healthy", value: "healthy" }
            ],
            onFilter: (value, record) => {
                const soh = record.currentSoh;
                if (value === "critical") return soh < 70;
                if (value === "warning") return soh >= 70 && soh < 85;
                if (value === "healthy") return soh >= 85;
                return false;
            },
            render: value => {
                let status, statusIcon;

                if (value >= 85) {
                    status = true; // Good - checkmark
                    statusIcon = "good";
                } else if (value >= 70) {
                    status = "warning"; // Warning - triangle
                    statusIcon = "warning";
                } else {
                    status = false; // Critical - X
                    statusIcon = "critical";
                }

                let evaluations = [];

                if (value >= 85) {
                    evaluations = [
                        {
                            status: true,
                            text: `${t("t_soh_condition")} ≥ 85%`,
                            hidden: false
                        }
                    ];
                } else if (value >= 70) {
                    evaluations = [
                        {
                            status: true,
                            text: `70% ≤ ${t("t_soh_condition")} < 85%)`,
                            hidden: false
                        }
                    ];
                } else {
                    evaluations = [
                        {
                            status: false,
                            text: `${t("t_soh_condition")} < 70%`,
                            hidden: false
                        }
                    ];
                }

                return (
                    <EvaluationPopover title="t_battery_status_evaluation" evaluations={evaluations}>
                        <CheckedIcon checked={status} />
                    </EvaluationPopover>
                );
            }
        },
        {
            title: <TextField value="t_nominal_capacity" />,
            dataIndex: "nominalCapacity",
            key: "nominalCapacity",
            sorter: (a, b) => a.nominalCapacity - b.nominalCapacity,
            render: value => <NumberField value={value} suffix=" kWh" decimals={1} />
        },
        {
            title: <TextField value="t_total_throughput" />,
            dataIndex: "totalThroughput",
            key: "totalThroughput",
            sorter: (a, b) => a.totalThroughput - b.totalThroughput,
            render: value => <NumberField value={value} suffix=" MWh" decimals={1} />
        },
        {
            title: <TextField value="t_battery_age" />,
            dataIndex: "batteryAge",
            key: "batteryAge",
            sorter: (a, b) => a.batteryAge - b.batteryAge,
            render: value => <NumberField value={value} suffix=" years" decimals={1} />
        }
    ];

    if (!fleet) {
        return (
            <SideHeader>
                <Row gutter={[10, 10]}>
                    <Col span={24}>
                        <ModuleSettings
                            title="t_soh_overview"
                            values={params}
                            viewOptions={{ items: TABLE_VIEW_ITEMS }}
                            fleetOptions={{ redirectUrl: clientUrls.modules.bm.dashboard() }}
                            onChange={values => setParams({ ...params, ...values })}
                        />
                    </Col>
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                </Row>
            </SideHeader>
        );
    }

    return (
        <SideHeader>
            <Row gutter={[10, 10]}>
                <Col span={24}>
                    <ModuleSettings
                        title="t_soh_overview"
                        values={params}
                        viewOptions={{ items: TABLE_VIEW_ITEMS }}
                        fleetOptions={{ redirectUrl: clientUrls.modules.bm.dashboard() }}
                        exportOptions={{ export: exportOptions }}
                        onChange={values => setParams({ ...params, ...values })}
                    />
                </Col>
                {fleet ? (
                    <Col span={24}>
                        <Space direction="vertical" size={20} style={{ width: "100%" }}>
                            {/* Page Header - Fleet Health Summary */}
                            {/* <Row gutter={16}>
                                <Col span={6}>
                                    <Card>
                                        <StatisticsBox
                                            title="t_fleet_average_soh"
                                            data={mockSohData.fleetStatistics.fleetAverageSoh}
                                            formatter={value => <NumberField value={value} suffix="%" decimals={1} />}
                                            valueStyle={{ color: "#3f8600" }}
                                        />
                                    </Card>
                                </Col>
                                <Col span={6}>
                                    <Card>
                                        <StatisticsBox
                                            title="t_critical_batteries"
                                            data={mockSohData.fleetStatistics.criticalBatteries}
                                            formatter={value => <NumberField value={value} decimals={0} />}
                                            // prefix={<Badge status="error" />}
                                            valueStyle={{ color: "#cf1322" }}
                                        />
                                    </Card>
                                </Col>
                                <Col span={6}>
                                    <Card>
                                        <StatisticsBox
                                            title="t_warning_batteries"
                                            data={mockSohData.sohDistribution.find(d => d.range === "70-79%")?.count || 0}
                                            formatter={value => <NumberField value={value} decimals={0} />}
                                            valueStyle={{ color: "#fa8c16" }}
                                        />
                                    </Card>
                                </Col>
                                <Col span={6}>
                                    <Card>
                                        <StatisticsBox
                                            title="t_healthy_batteries"
                                            data={mockSohData.sohDistribution
                                                .filter(d => d.range === "90-100%" || d.range === "80-89%")
                                                .reduce((sum, d) => sum + d.count, 0)}
                                            formatter={value => <NumberField value={value} decimals={0} />}
                                            valueStyle={{ color: "#52c41a" }}
                                        />
                                    </Card>
                                </Col>
                            </Row> */}

                            {/* Main Data Table */}
                            <Card
                                title="Batterie Overview"
                                stretchHeight
                                extra={
                                    <RangeSelector
                                        value={selectedRange}
                                        onChange={setSelectedRange}
                                        ranges={customRanges}
                                        showTime={false}
                                        allowClear={false}
                                    />
                                }
                            >
                                <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                    {/* Top Row: Histogram on left, SOH Stats on right */}
                                    <Row gutter={20}>
                                        {/* SOH Distribution Histogram on the left */}
                                        <Col xs={24} lg={12}>
                                            <Card size="small" title="SOH Distribution">
                                                <ColumnChart
                                                    chartConfig={{
                                                        data: sohHistogramData,
                                                        xField: "range",
                                                        yField: "count",
                                                        height: 300,
                                                        xAxis: {
                                                            title: {
                                                                text: "SOH Range (%)"
                                                            }
                                                        },
                                                        yAxis: {
                                                            title: {
                                                                text: "Number of Batteries"
                                                            }
                                                        },
                                                        loading: false
                                                    }}
                                                />
                                            </Card>
                                        </Col>

                                        {/* SOH Statistics on the right */}
                                        <Col xs={24} lg={12}>
                                            <Card height={370}>
                                                <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                                    <div style={{ textAlign: "center" }}>
                                                        <Statistic title="Minimum SoH" value={sohStatistics.minimum} suffix="%" precision={1} />
                                                    </div>
                                                    <Divider style={{ margin: "8px 0" }} />
                                                    <div style={{ textAlign: "center" }}>
                                                        <Statistic title="Average SoH" value={sohStatistics.average} suffix="%" precision={1} />
                                                    </div>
                                                    <Divider style={{ margin: "8px 0" }} />
                                                    <div style={{ textAlign: "center" }}>
                                                        <Statistic title="Maximum SoH" value={sohStatistics.maximum} suffix="%" precision={1} />
                                                    </div>
                                                </Space>
                                            </Card>
                                        </Col>
                                    </Row>

                                    {/* Bottom Row: Table full width */}
                                    <Card>
                                        <Table
                                            dataSource={mockSohData.batteryData}
                                            columns={sohTableColumns}
                                            rowKey="key"
                                            pagination={{
                                                pageSize: 20,
                                                showSizeChanger: true,
                                                showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />
                                            }}
                                            size="small"
                                        />
                                    </Card>
                                </Space>
                            </Card>

                            {/* Bottom Section - Actionable Insights */}
                            {/* <Row gutter={[20, 20]}>
                                <Col lg={8} xs={24}>
                                    <Widget title="t_immediate_actions" height={300}>
                                        <Space direction="vertical" style={{ width: "100%" }}>
                                            {mockSohData.actionableInsights.immediateActions.map((item, index) => (
                                                <Card key={index} size="small" style={{ backgroundColor: "#fff2f0", border: "1px solid #ffccc7" }}>
                                                    <Space direction="vertical" size={4}>
                                                        <Text strong>{item.batteryId}</Text>
                                                        <Text>{item.action}</Text>
                                                        <Text type="danger">SOH: {item.soh}%</Text>
                                                        <Text type="secondary">{item.reason}</Text>
                                                    </Space>
                                                </Card>
                                            ))}
                                        </Space>
                                    </Widget>
                                </Col>

                                <Col lg={8} xs={24}>
                                    <Widget title="t_planned_maintenance" height={300}>
                                        <Space direction="vertical" style={{ width: "100%" }}>
                                            {mockSohData.actionableInsights.plannedMaintenance.map((item, index) => (
                                                <Card key={index} size="small" style={{ backgroundColor: "#fffbe6", border: "1px solid #ffe58f" }}>
                                                    <Space direction="vertical" size={4}>
                                                        <Text strong>{item.batteryId}</Text>
                                                        <Text>{item.action}</Text>
                                                        <Text type="warning">By: {item.estimatedDate}</Text>
                                                        <Text type="secondary">{item.reason}</Text>
                                                    </Space>
                                                </Card>
                                            ))}
                                        </Space>
                                    </Widget>
                                </Col>

                                <Col lg={8} xs={24}>
                                    <Widget title="t_optimization_opportunities" height={300}>
                                        <Space direction="vertical" style={{ width: "100%" }}>
                                            {mockSohData.actionableInsights.optimizationOpportunities.map((item, index) => (
                                                <Card key={index} size="small" style={{ backgroundColor: "#f6ffed", border: "1px solid #b7eb8f" }}>
                                                    <Space direction="vertical" size={4}>
                                                        <Text strong>{item.finding}</Text>
                                                        <Text>{item.recommendation}</Text>
                                                        <Text type="secondary">Affected: {item.affectedBatteries.join(", ")}</Text>
                                                    </Space>
                                                </Card>
                                            ))}
                                        </Space>
                                    </Widget>
                                </Col>
                            </Row> */}
                        </Space>
                    </Col>
                ) : (
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                )}
            </Row>
        </SideHeader>
    );
};

export default BmSohComponent;
