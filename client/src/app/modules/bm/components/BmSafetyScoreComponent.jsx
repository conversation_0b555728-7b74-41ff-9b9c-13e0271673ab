import React, { useMemo, useState } from "react";
import { Row, Col, Space, Divider, Typography, Popover, List, Statistic, Table as AntTable } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { clientUrls } from "misc/urls";
import { useSubfleets } from "misc/api/fleetconfiguration";
import { TextField, NumberField, DatetimeField } from "misc/fields";
import { Column<PERSON>hart, LineChart } from "misc/charts";
import { Table } from "misc/tables";
import { Widget } from "misc/widgets";
import { BadgeElement } from "misc/badges";
import { StatisticsBox } from "misc/boxes";
import { RangeSelector } from "misc/selectors";

import SideHeader from "navigation/SideHeader";
import ModuleSettings from "modules/ModuleSettings";
import { TABLE_VIEW_ITEMS } from "modules/ModuleSettings";

import { stsColors } from "styles";

// Import mock data
import mockSafetyData from "./data.json";

const { Text } = Typography;

const SAFETY_SCORE_RANGES = [
    { min: 0, max: 1, label: "0-1 (Excellent)", color: "#52c41a", level: "excellent" },
    { min: 2, max: 3, label: "2-3 (Warning)", color: "#faad14", level: "warning" },
    { min: 4, max: 5, label: "4-5 (Critical)", color: "#f5222d", level: "critical" }
];

const CheckedIcon = ({ checked, colorChecked = stsColors.green2, colorUnchecked = stsColors.red2, colorWarning = stsColors.yellow2 }) => {
    if (checked === null) return <TextField />;

    if (checked === "warning") {
        return <FontAwesomeIcon icon={["fas", "triangle-exclamation"]} color={colorWarning} />;
    }

    return checked ? (
        <FontAwesomeIcon icon={["fas", "check"]} color={colorChecked} />
    ) : (
        <FontAwesomeIcon icon={["fas", "xmark"]} color={colorUnchecked} />
    );
};

const EvaluationPopover = ({ title = "t_battery_safety_evaluation", evaluations, children }) => {
    const data = evaluations.filter(evaluation => !evaluation?.hidden);

    return (
        <Popover
            overlayInnerStyle={{ padding: "0 -16px", minWidth: "400px" }}
            title={<TextField value={title} />}
            placement="topLeft"
            trigger="click"
            content={
                <div>
                    <List
                        size="small"
                        split={false}
                        dataSource={data}
                        renderItem={item => (
                            <List.Item style={{ padding: "2px 0" }}>
                                <List.Item.Meta avatar={<CheckedIcon checked={item.status} />} title={item.text} />
                            </List.Item>
                        )}
                    />
                </div>
            }
        >
            <div style={{ cursor: "pointer" }}>{children}</div>
        </Popover>
    );
};

const BmSafetyScoreComponent = () => {
    const { t } = useTranslation();
    const { subfleets } = useSubfleets({ suspense: true });

    // State for date range selector
    const [selectedRange, setSelectedRange] = useState([dayjs().subtract(1, "month").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);

    const { params, setParams } = useParams({
        options: [{ name: "fleet", persist: "global", allowed: value => subfleets.find(fleet => fleet.subfleetId === value) }]
    });

    const fleet = useMemo(() => subfleets.find(fleet => fleet.subfleetId === params.fleet), [params.fleet, subfleets]);

    // Process Safety Score data for histogram
    const safetyScoreHistogramData = useMemo(() => {
        const safetyValues = mockSafetyData.batteryData.map(battery => battery.safetyScore);
        const minScore = Math.min(...safetyValues);
        const maxScore = Math.max(...safetyValues);

        // Create bins with 1-unit intervals for safety scores (0-1, 1-2, 2-3, 3-4, 4-5)
        const binSize = 1;
        const startRange = Math.floor(minScore / binSize) * binSize;
        const endRange = Math.ceil(maxScore / binSize) * binSize;

        const bins = [];
        for (let i = startRange; i <= endRange; i += binSize) {
            const nextBin = i + binSize;
            bins.push({
                range: `${i}-${nextBin}`,
                minValue: i,
                maxValue: nextBin,
                count: 0
            });
        }

        // Count batteries in each bin
        safetyValues.forEach(score => {
            const binIndex = Math.floor((score - startRange) / binSize);
            if (bins[binIndex]) {
                bins[binIndex].count++;
            }
        });

        return bins.filter(bin => bin.count > 0); // Only show bins with data
    }, []);

    // Calculate actual Safety Score statistics from data
    const safetyScoreStatistics = useMemo(() => {
        const safetyValues = mockSafetyData.batteryData.map(battery => battery.safetyScore);
        const minScore = Math.min(...safetyValues);
        const maxScore = Math.max(...safetyValues);
        const avgScore = safetyValues.reduce((sum, score) => sum + score, 0) / safetyValues.length;

        return {
            minimum: minScore,
            average: avgScore,
            maximum: maxScore
        };
    }, []);

    // Calculate battery counts by safety status
    const safetyScoreCounts = useMemo(() => {
        const safetyValues = mockSafetyData.batteryData.map(battery => battery.safetyScore);

        const criticalCount = safetyValues.filter(score => score >= 4).length;
        const warningCount = safetyValues.filter(score => score >= 2 && score < 4).length;
        const healthyCount = safetyValues.filter(score => score < 2).length;

        return {
            critical: criticalCount,
            warning: warningCount,
            healthy: healthyCount
        };
    }, []);

    // Custom range options for the selector
    const customRanges = useMemo(
        () => [
            { label: "t_range_last_week", value: [dayjs().subtract(1, "week"), dayjs()] },
            { label: "t_range_last_two_weeks", value: [dayjs().subtract(2, "weeks"), dayjs()] },
            { label: "t_range_last_month", value: [dayjs().subtract(1, "month"), dayjs()] }
        ],
        []
    );

    const exportOptions = useMemo(
        () => ({
            label: "t_safety_score_overview",
            columns: [
                { value: "batteryId", label: "t_battery_id" },
                { value: "safetyScore", label: "t_safety_score" },
                { value: "currentSoh", label: "t_soh" },
                { value: "currentVin", label: "t_current_vehicle" },
                { value: "daysSinceLastMeasurement", label: "t_days_since_measurement" },
                { value: "actionRequired", label: "t_action_required" },
                { value: "nominalCapacity", label: "t_nominal_capacity" },
                { value: "totalThroughput", label: "t_total_throughput" },
                { value: "batteryAge", label: "t_battery_age" }
            ],
            request: {
                method: "GET",
                url: "/mock/safety-export", // Mock endpoint since we're using local data
                filter: {
                    hierarchy: fleet?.organisationalUnits,
                    vehicles: fleet?.vehicles,
                    range: selectedRange
                }
            }
        }),
        [fleet, selectedRange]
    );

    const safetyScoreTableColumns = [
        {
            title: <TextField value="t_battery_id" />,
            dataIndex: "batteryId",
            key: "batteryId",
            render: text => <a href={`#battery/${text}`}>{text}</a>
        },
        {
            title: <TextField value="t_vin" />,
            dataIndex: "currentVin",
            key: "currentVin",
            render: vin => vin || <TextField value="t_not_assigned" />
        },
        {
            title: <TextField value="t_safety_score" />,
            dataIndex: "safetyScore",
            key: "safetyScore",
            sorter: (a, b) => a.safetyScore - b.safetyScore,
            render: value => <NumberField value={value} decimals={1} />
        },
        {
            title: <TextField value="t_safety_status" />,
            dataIndex: "safetyScore",
            key: "safetyStatus",
            filters: [
                { text: "Critical", value: "critical" },
                { text: "Warning", value: "warning" },
                { text: "Excellent", value: "excellent" }
            ],
            onFilter: (value, record) => {
                const score = record.safetyScore;
                if (value === "critical") return score >= 4;
                if (value === "warning") return score >= 2 && score < 4;
                if (value === "excellent") return score < 2;
                return false;
            },
            render: value => {
                let status, statusIcon;

                if (value < 2) {
                    status = true; // Excellent - checkmark
                    statusIcon = "excellent";
                } else if (value < 4) {
                    status = "warning"; // Warning - triangle
                    statusIcon = "warning";
                } else {
                    status = false; // Critical - X
                    statusIcon = "critical";
                }

                let evaluations = [];

                if (value < 2) {
                    evaluations = [
                        {
                            status: true,
                            text: `${t("t_safety_score")} < 2 (Excellent)`,
                            hidden: false
                        }
                    ];
                } else if (value < 4) {
                    evaluations = [
                        {
                            status: true,
                            text: `2 ≤ ${t("t_safety_score")} < 4 (Warning)`,
                            hidden: false
                        }
                    ];
                } else {
                    evaluations = [
                        {
                            status: false,
                            text: `${t("t_safety_score")} ≥ 4 (Critical)`,
                            hidden: false
                        }
                    ];
                }

                return (
                    <EvaluationPopover title="t_battery_safety_evaluation" evaluations={evaluations}>
                        <CheckedIcon checked={status} />
                    </EvaluationPopover>
                );
            }
        },
        {
            title: <TextField value="t_soh" />,
            dataIndex: "currentSoh",
            key: "currentSoh",
            sorter: (a, b) => a.currentSoh - b.currentSoh,
            render: value => <NumberField value={value} suffix="%" decimals={1} />
        },
        {
            title: <TextField value="t_nominal_capacity" />,
            dataIndex: "nominalCapacity",
            key: "nominalCapacity",
            sorter: (a, b) => a.nominalCapacity - b.nominalCapacity,
            render: value => <NumberField value={value} suffix=" kWh" decimals={1} />
        },
        {
            title: <TextField value="t_total_throughput" />,
            dataIndex: "totalThroughput",
            key: "totalThroughput",
            sorter: (a, b) => a.totalThroughput - b.totalThroughput,
            render: value => <NumberField value={value} suffix=" MWh" decimals={1} />
        },
        {
            title: <TextField value="t_battery_age" />,
            dataIndex: "batteryAge",
            key: "batteryAge",
            sorter: (a, b) => a.batteryAge - b.batteryAge,
            render: value => <NumberField value={value} suffix=" years" decimals={1} />
        }
    ];

    if (!fleet) {
        return (
            <SideHeader>
                <Row gutter={[10, 10]}>
                    <Col span={24}>
                        <ModuleSettings
                            title="t_safety_score_overview"
                            values={params}
                            viewOptions={{ items: TABLE_VIEW_ITEMS }}
                            fleetOptions={{ redirectUrl: clientUrls.modules.bm.dashboard() }}
                            onChange={values => setParams({ ...params, ...values })}
                        />
                    </Col>
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                </Row>
            </SideHeader>
        );
    }

    return (
        <SideHeader>
            <Row gutter={[10, 10]}>
                <Col span={24}>
                    <ModuleSettings
                        title="t_safety_score_overview"
                        values={params}
                        viewOptions={{ items: TABLE_VIEW_ITEMS }}
                        fleetOptions={{ redirectUrl: clientUrls.modules.bm.dashboard() }}
                        exportOptions={{ export: exportOptions }}
                        onChange={values => setParams({ ...params, ...values })}
                    />
                </Col>
                {fleet ? (
                    <Col span={24}>
                        <Space direction="vertical" size={20} style={{ width: "100%" }}>
                            {/* Main Data Table */}
                            <Card
                                title="Safety Score"
                                stretchHeight
                                extra={
                                    <RangeSelector
                                        value={selectedRange}
                                        onChange={setSelectedRange}
                                        ranges={customRanges}
                                        showTime={false}
                                        allowClear={false}
                                    />
                                }
                            >
                                <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                    {/* Battery Count Statistics - Horizontal Layout */}
                                    <Card>
                                        <Row align="middle" justify="space-around" style={{ padding: "20px 0" }}>
                                            <Col>
                                                <div style={{ textAlign: "center" }}>
                                                    <Statistic
                                                        title="Healthy Batteries"
                                                        value={safetyScoreCounts.healthy}
                                                        precision={0}
                                                        valueStyle={{ color: stsColors.green2 }}
                                                    />
                                                </div>
                                            </Col>
                                            <Divider type="vertical" style={{ height: "60px" }} />
                                            <Col>
                                                <div style={{ textAlign: "center" }}>
                                                    <Statistic
                                                        title="Warning Batteries"
                                                        value={safetyScoreCounts.warning}
                                                        precision={0}
                                                        valueStyle={{ color: stsColors.yellow2 }}
                                                    />
                                                </div>
                                            </Col>
                                            <Divider type="vertical" style={{ height: "60px" }} />
                                            <Col>
                                                <div style={{ textAlign: "center" }}>
                                                    <Statistic
                                                        title="Critical Batteries"
                                                        value={safetyScoreCounts.critical}
                                                        precision={0}
                                                        valueStyle={{ color: stsColors.red2 }}
                                                    />
                                                </div>
                                            </Col>
                                        </Row>
                                    </Card>

                                    {/* Bottom Row: Table full width */}
                                    <Card>
                                        <Table
                                            dataSource={mockSafetyData.batteryData}
                                            columns={safetyScoreTableColumns}
                                            rowKey="key"
                                            pagination={{
                                                pageSize: 20,
                                                showSizeChanger: true,
                                                showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />
                                            }}
                                            size="small"
                                        />
                                    </Card>
                                </Space>
                            </Card>
                        </Space>
                    </Col>
                ) : (
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                )}
            </Row>
        </SideHeader>
    );
};

export default BmSafetyScoreComponent;
