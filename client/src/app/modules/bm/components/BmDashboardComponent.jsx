import dayjs from "dayjs";
import { Row, Col } from "antd";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { clientUrls, datalayerUrls } from "misc/urls";
import { useSubfleets } from "misc/api/fleetconfiguration";
import { BatteryFleetTable } from "misc/widgets/consumers";
import { TextField, DateField, NumberField } from "misc/fields";
import { Table } from "misc/tables";

import ModuleSettings from "app/modules/ModuleSettings";
import { TABLE_VIEW_ITEMS } from "modules/ModuleSettings";

const VIEW_ITEMS = [
    ...TABLE_VIEW_ITEMS
    // Removed batteryColumns configuration since columns are now fixed
];

const BatteryFleetView = () => {
    const [t] = useTranslation();
    const { subfleets } = useSubfleets({ suspense: true });

    const { params, setParams } = useParams({
        options: [
            { name: "fleet", persist: "global", allowed: value => subfleets.find(fleet => fleet.subfleetId === value) },
            { name: "range", defaultValue: [dayjs().subtract(30, "days").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")] },
            { name: "columns", defaultValue: ["vin", "batteryId"], persist: "site" },
            { name: "fixedColumn", defaultValue: "vin", persist: "site" }
            // Removed batteryColumns parameter since columns are now fixed
        ]
    });

    const fleet = useMemo(() => subfleets.find(fleet => fleet.subfleetId === params.fleet), [params.fleet, subfleets]);

    const exportOptions = useMemo(
        () => ({
            label: "t_battery_history",
            columns: [
                { value: "batteryId", label: "t_battery_id" },
                { value: "type", label: "t_battery_type" },
                { value: "nominalCapacityKwh", label: "t_nominal_capacity" },
                { value: "currentVin", label: "t_vin" },
                { value: "batteryAge", label: "t_battery_age" },
                { value: "totalMileage", label: "t_total_mileage" },
                { value: "vin", label: "t_vin" },
                { value: "licensePlate", label: "t_license_plate" },
                { value: "organisationalUnit", label: "t_organisational_unit" },
                { value: "manufacturer", label: "t_manufacturer" },
                { value: "model", label: "t_model" }
            ],
            request: {
                method: "GET",
                url: datalayerUrls.fleet.widgets.list(),
                params: { type: "BmDashboardComponent" },
                filter: {
                    hierarchy: fleet?.organisationalUnits,
                    vehicles: fleet?.vehicles,
                    range: params.range
                }
            }
        }),
        [fleet, params.range]
    );

    return (
        <Row gutter={[10, 10]}>
            <Col span={24}>
                <ModuleSettings
                    values={params}
                    viewOptions={{ items: VIEW_ITEMS }}
                    exportOptions={{ export: exportOptions }}
                    fleetOptions={{ redirectUrl: clientUrls.modules.vlv.fleetConfiguration() }}
                    onChange={values => setParams({ ...params, ...values })}
                />
            </Col>
            {fleet ? (
                <Col span={24}>
                    <Card title="t_bm_dashboard">
                        <BatteryFleetTable
                            filter={{
                                hierarchy: fleet.organisationalUnits,
                                vehicles: fleet.vehicles,
                                range: params.range
                            }}
                            view={{
                                columns: params.columns,
                                fixedColumn: params.fixedColumn
                            }}
                        />
                    </Card>
                </Col>
            ) : (
                <Col span={24}>
                    <Card height={500}>
                        <Result type="noFleetSelected" url={clientUrls.modules.vlv.fleetConfiguration()} />
                    </Card>
                </Col>
            )}
        </Row>
    );
};

const BmDashboardComponent = () => {
    return <BatteryFleetView />;
};

export default BmDashboardComponent;
