import dayjs from "dayjs";
import { Row, Col, Space, Statistic, Table as AntTable } from "antd";
import React, { useMemo, useState } from "react";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { clientUrls } from "misc/urls";
import { useSubfleets } from "misc/api/fleetconfiguration";
import { TextField, NumberField, DateField } from "misc/fields";
import { ColumnChart } from "misc/charts";
import { Table } from "misc/tables";
import { RangeSelector } from "misc/selectors";

import SideHeader from "navigation/SideHeader";
import ModuleSettings from "modules/ModuleSettings";
import { TABLE_VIEW_ITEMS } from "modules/ModuleSettings";

import mockData from "./data.json";

const VIEW_ITEMS = [...TABLE_VIEW_ITEMS];

const BmDashboardComponentUnify = () => {
    const { subfleets } = useSubfleets({ suspense: true });

    const { params, setParams } = useParams({
        options: [
            { name: "fleet", persist: "global", allowed: value => subfleets.find(fleet => fleet.subfleetId === value) },
            { name: "range", defaultValue: [dayjs().subtract(30, "days").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")] }
        ]
    });

    const fleet = useMemo(() => subfleets.find(fleet => fleet.subfleetId === params.fleet), [params.fleet, subfleets]);

    // Custom date ranges for the RangeSelector
    const customRanges = [
        "lastWeek",
        "lastTwoWeeks",
        "lastFourWeeks",
        {
            label: "t_last_3_months",
            value: [dayjs().subtract(3, "months"), dayjs()]
        },
        {
            label: "t_last_6_months",
            value: [dayjs().subtract(6, "months"), dayjs()]
        }
    ];

    const [selectedRange, setSelectedRange] = useState(params.range);

    // Process SOH data for histogram (exact same as BmSohComponent)
    const sohHistogramData = useMemo(() => {
        const sohValues = mockData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);

        // Create bins with 5% intervals
        const binSize = 5;
        const startRange = Math.floor(minSoh / binSize) * binSize;
        const endRange = Math.ceil(maxSoh / binSize) * binSize;

        const bins = [];
        for (let i = startRange; i <= endRange; i += binSize) {
            bins.push({
                range: `${i}-${i + binSize - 1}%`,
                minValue: i,
                maxValue: i + binSize - 1,
                count: 0
            });
        }

        // Count batteries in each bin
        sohValues.forEach(soh => {
            const binIndex = Math.floor((soh - startRange) / binSize);
            if (bins[binIndex]) {
                bins[binIndex].count++;
            }
        });

        return bins.filter(bin => bin.count > 0); // Only show bins with data
    }, []);

    // Calculate actual SOH statistics from data (exact same as BmSohComponent)
    const sohStatistics = useMemo(() => {
        const sohValues = mockData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);
        const avgSoh = sohValues.reduce((sum, soh) => sum + soh, 0) / sohValues.length;

        return {
            minimum: minSoh,
            average: avgSoh,
            maximum: maxSoh
        };
    }, []);

    // Calculate Safety Score statistics
    const safetyStats = useMemo(() => {
        const safetyValues = mockData.batteryData.map(battery => battery.safetyScore);
        const minSafety = Math.min(...safetyValues);
        const maxSafety = Math.max(...safetyValues);
        const avgSafety = safetyValues.reduce((sum, score) => sum + score, 0) / safetyValues.length;

        return {
            minimum: minSafety,
            average: avgSafety,
            maximum: maxSafety
        };
    }, []);

    // Table columns configuration
    const tableColumns = [
        {
            title: <TextField value="t_battery_id" />,
            dataIndex: "batteryId",
            key: "batteryId",
            sorter: (a, b) => a.batteryId.localeCompare(b.batteryId),
            width: 120,
            fixed: "left"
        },
        {
            title: <TextField value="t_battery_type" />,
            dataIndex: "type",
            key: "type",
            render: () => "Li-Ion", // Mock data doesn't have type
            width: 100
        },
        {
            title: <TextField value="t_vin" />,
            dataIndex: "currentVin",
            key: "currentVin",
            render: vin => vin || <span style={{ color: "#999" }}>Unassigned</span>,
            width: 140
        },
        {
            title: <TextField value="t_nominal_capacity" />,
            dataIndex: "nominalCapacity",
            key: "nominalCapacity",
            render: value => <NumberField value={value} decimals={1} suffix=" kWh" />,
            sorter: (a, b) => a.nominalCapacity - b.nominalCapacity,
            width: 120
        },
        {
            title: <TextField value="t_battery_age" />,
            dataIndex: "batteryAge",
            key: "batteryAge",
            render: value => <NumberField value={value} decimals={1} suffix=" years" />,
            sorter: (a, b) => a.batteryAge - b.batteryAge,
            width: 100
        },
        {
            title: <TextField value="t_total_mileage" />,
            dataIndex: "totalThroughput",
            key: "totalThroughput",
            render: value => <NumberField value={value * 1000} decimals={0} suffix=" km" />,
            sorter: (a, b) => a.totalThroughput - b.totalThroughput,
            width: 120
        },
        {
            title: <TextField value="t_soh" />,
            dataIndex: "currentSoh",
            key: "currentSoh",
            render: value => <NumberField value={value} decimals={1} suffix="%" />,
            sorter: (a, b) => a.currentSoh - b.currentSoh,
            width: 80
        },
        {
            title: <TextField value="t_safety_score" />,
            dataIndex: "safetyScore",
            key: "safetyScore",
            render: value => <NumberField value={value} decimals={1} />,
            sorter: (a, b) => a.safetyScore - b.safetyScore,
            width: 100
        }
    ];

    // Expandable row render function for installation history
    const renderExpandedRow = record => {
        const historyColumns = [
            {
                title: <TextField value="t_vin" />,
                dataIndex: "vin",
                key: "vin",
                width: 140
            },
            {
                title: <TextField value="t_license_plate" />,
                dataIndex: "licensePlate",
                key: "licensePlate",
                width: 120
            },
            {
                title: <TextField value="t_installation_date" />,
                dataIndex: "installationDate",
                key: "installationDate",
                render: date => <DateField value={date} format="DD.MM.YYYY" />,
                width: 120
            },
            {
                title: <TextField value="t_removal_date" />,
                dataIndex: "removalDate",
                key: "removalDate",
                render: date => (date ? <DateField value={date} format="DD.MM.YYYY" /> : <span style={{ color: "#999" }}>Current</span>),
                width: 120
            },
            {
                title: <TextField value="t_duration" />,
                dataIndex: "durationDays",
                key: "durationDays",
                render: days => <NumberField value={days} decimals={0} suffix=" days" />,
                width: 100
            },
            {
                title: <TextField value="t_mileage" />,
                dataIndex: "kilometersInVehicle",
                key: "kilometersInVehicle",
                render: km => <NumberField value={km} decimals={0} suffix=" km" />,
                width: 120
            },
            {
                title: <TextField value="t_organisational_unit" />,
                dataIndex: "organisationalUnit",
                key: "organisationalUnit",
                width: 150
            },
            {
                title: <TextField value="t_model" />,
                dataIndex: "model",
                key: "model",
                width: 100
            }
        ];

        return (
            <div style={{ padding: "16px", backgroundColor: "#fafafa" }}>
                <TextField value="t_battery_installation_history" level={5} style={{ marginBottom: 16 }} />
                <AntTable
                    dataSource={record.installationHistory?.map((item, index) => ({ ...item, key: index })) || []}
                    columns={historyColumns}
                    size="small"
                    pagination={false}
                    scroll={{ x: "100%" }}
                    bordered
                />
            </div>
        );
    };

    return (
        <SideHeader>
            <Row gutter={[10, 10]}>
                <Col span={24}>
                    <ModuleSettings
                        values={params}
                        viewOptions={{ items: VIEW_ITEMS }}
                        fleetOptions={{ redirectUrl: clientUrls.modules.vlv.fleetConfiguration() }}
                        onChange={values => setParams({ ...params, ...values })}
                    />
                </Col>

                {fleet ? (
                    <Col span={24}>
                        <Card
                            title="Battery Management Dashboard"
                            extra={
                                <RangeSelector
                                    value={selectedRange}
                                    onChange={setSelectedRange}
                                    ranges={customRanges}
                                    showTime={false}
                                    allowClear={false}
                                />
                            }
                        >
                            <Space direction="vertical" size={24} style={{ width: "100%" }}>
                                {/* First Row: SOH Distribution + SOH Degradation Pattern */}
                                <Row gutter={[16, 16]}>
                                    <Col xs={24} lg={12}>
                                        <Card title="SOH Distribution" height={416} size="small">
                                            <ColumnChart
                                                chartConfig={{
                                                    data: sohHistogramData,
                                                    xField: "range",
                                                    yField: "count",
                                                    height: 350,
                                                    xAxis: {
                                                        title: {
                                                            text: "SOH Range (%)"
                                                        }
                                                    },
                                                    yAxis: {
                                                        title: {
                                                            text: "Number of Batteries"
                                                        }
                                                    },
                                                    loading: false
                                                }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={24} lg={12}>
                                        <Card title="SOH Degradation Pattern" height={416} size="small">
                                            <div
                                                style={{
                                                    height: "100%",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    color: "#999",
                                                    fontSize: "14px"
                                                }}
                                            >
                                                To be implemented - SOH Scatterplot
                                            </div>
                                        </Card>
                                    </Col>
                                </Row>

                                {/* Second Row: SOH Stats + Safety Score Stats as individual small cards */}
                                <Row gutter={[16, 16]}>
                                    {/* SOH Statistics */}
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Min SOH"
                                                value={sohStatistics.minimum}
                                                suffix="%"
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Avg SOH"
                                                value={sohStatistics.average}
                                                suffix="%"
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Max SOH"
                                                value={sohStatistics.maximum}
                                                suffix="%"
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>

                                    {/* Safety Score Statistics */}
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Min Safety"
                                                value={safetyStats.minimum}
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Avg Safety"
                                                value={safetyStats.average}
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={12} sm={8} lg={4}>
                                        <Card size="small" style={{ textAlign: "center", height: 120 }}>
                                            <Statistic
                                                title="Max Safety"
                                                value={safetyStats.maximum}
                                                precision={1}
                                                valueStyle={{ fontSize: "18px" }}
                                            />
                                        </Card>
                                    </Col>
                                </Row>

                                {/* Unified Battery Table */}
                                <div>
                                    <TextField value="Battery Details" level={4} style={{ marginBottom: 16 }} />
                                    <Table
                                        dataSource={mockData.batteryData}
                                        columns={tableColumns}
                                        expandable={{
                                            expandedRowRender: renderExpandedRow,
                                            rowExpandable: record => !!record.installationHistory?.length
                                        }}
                                        rowKey="key"
                                        scroll={{ x: 1200 }}
                                        size="small"
                                        pagination={{
                                            pageSize: 20,
                                            showSizeChanger: true,
                                            showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />
                                        }}
                                    />
                                </div>
                            </Space>
                        </Card>
                    </Col>
                ) : (
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                )}
            </Row>
        </SideHeader>
    );
};

export default BmDashboardComponentUnify;
