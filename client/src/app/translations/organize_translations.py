import subprocess
from collections import OrderedDict
import json


def importStringsBaseKeys():
    # Get keys from frontend
    bashCommand = r"egrep -rho \bt_[a-z0-9_]+  --include=*.jsx --include=*.js .. --exclude-dir=node_modules"
    egrep = subprocess.run(bashCommand.split(), stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
    allKeys = egrep.stdout.decode("utf-8").split()

    # Get dynamic generated keys
    with open("src/app/translations/dynamic_keys.json", "r+") as json_file:
        dynamicKeys = json.load(json_file)
        dynamicKeys = set(dynamicKeys).difference(allKeys)
        dynamicKeys = list(sorted(dynamicKeys))
        allKeys += dynamicKeys

        with open("src/app/translations/dynamic_keys.json", "w") as json_file:
            json.dump(dynamicKeys, json_file, indent=2)

    allKeys = set(allKeys)
    return sorted(list(allKeys))


def checkIfLangHasAllKeys(lang, keys):
    with open("src/app/translations/" + lang + ".json", "r+") as json_file:
        langDict = json.load(json_file)
        count = 0
        for key in keys:
            try:
                if langDict[key] == "!MISSING_KEY!":
                    raise Exception()
            except:
                langDict[key] = "!MISSING_KEY!"
                count += 1
                print(lang + ":", key)
        print("Missing count", lang, count)
        with open("src/app/translations/" + lang + ".json", "w") as json_file:
            json.dump(langDict, json_file, indent=2)


def removeAdditionalKeys(lang, keys):
    with open("src/app/translations/" + lang + ".json", "r") as json_file:
        langDict = json.load(json_file)

        keysToRemove = list(set(langDict) - set(keys))

        for key in keysToRemove:
            del langDict[key]

        with open("src/app/translations/" + lang + ".json", "w") as json_file:
            json.dump(langDict, json_file, indent=2)


def orderLangAlphabetically(lang):
    with open("src/app/translations/" + lang + ".json", "r") as json_file:
        langDict = json.load(json_file)
        orderedLang = OrderedDict(sorted(langDict.items()))
        with open("src/app/translations/" + lang + ".json", "w") as json_file:
            json.dump(orderedLang, json_file, indent=2)


def main():
    languages = ["de", "en", "jp"]

    baseKeys = list(filter(lambda key: not key.endswith("_"), importStringsBaseKeys()))

    print("Keys count:", len(baseKeys))
    print("Missing keys:")
    for lang in languages:
        checkIfLangHasAllKeys(lang, baseKeys)
        removeAdditionalKeys(lang, baseKeys)
        orderLangAlphabetically(lang)


if __name__ == "__main__":
    main()
