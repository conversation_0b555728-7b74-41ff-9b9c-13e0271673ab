{"t_absolute": "!MISSING_KEY!", "t_abt": "ABT", "t_add": "Hinzufügen", "t_add_item": "Eintrag hinzufügen", "t_alemania_1": "Alemania 1", "t_alemania_2": "Alemania 2", "t_all": "Alle", "t_ambient_temperature": "Umgebungstemperatur", "t_ambient_temperature_charging_stats_max": "Max. Temperatur Ladestatistiken", "t_ambient_temperature_charging_stats_max_description": "Max. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Ladestatistiken", "t_ambient_temperature_charging_stats_min": "Min. Temperatur Ladestatistiken", "t_ambient_temperature_charging_stats_min_description": "Min. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Ladestatistiken", "t_ambient_temperature_histogram": "Umgebungstemperatur", "t_ambient_temperature_histogram_description_x_axis": "X-Achse: Zeiteinheit (Tag, Woche)", "t_ambient_temperature_histogram_description_y_axis": "Y-Achse: Min. bzw. Max. Umgebungstemperatur (in °C) pro Zeiteinheit", "t_ambient_temperature_max": "<PERSON><PERSON>", "t_ambient_temperature_max_description": "Max. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Signale, Ladestatistiken", "t_ambient_temperature_min": "<PERSON><PERSON>", "t_ambient_temperature_min_description": "Min. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Signale, Ladestatistiken", "t_ambient_temperature_normalized_signals_max": "Max. Temperatur Signale", "t_ambient_temperature_normalized_signals_max_description": "Max. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Signale", "t_ambient_temperature_normalized_signals_min": "Min. Temperatur Signale", "t_ambient_temperature_normalized_signals_min_description": "Max. Umgebungstemperatur pro Zeiteinheit.\nDatenquelle: Signale", "t_apply": "<PERSON><PERSON><PERSON>", "t_arminia": "Arminia", "t_ascend": "Aufsteigend", "t_availability": "Verfügbarkeit", "t_available": "Verfügbar", "t_available_organisational_units": "Verfügbare Organisationseinheiten", "t_available_users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t_available_vehicles": "Verfügbare Fahrzeuge", "t_average": "Durchschnitt", "t_average_number_of_vehicles": "Durch<PERSON> An<PERSON><PERSON> Fahrzeuge", "t_avg_distance_per_trip": "Strecke / Fahrt", "t_avg_door_open_per_km": "Türöffnungen / km", "t_avg_door_open_per_trip": "Türöffnungen / Fahrt", "t_avg_trip_duration": "<PERSON><PERSON><PERSON>", "t_battery": "<PERSON><PERSON>ie", "t_battery_size_36_to_43kwh": "36kWh bis 43kWh", "t_battery_size_30_to_36kwh": "30Kwh bis 36kWh", "t_battery_size_less_than_30kw": "weniger als 30kWh", "t_battery_size_more_than_60kwh": "mehr als 60kWh", "t_battery_overview": "Batterieübersicht", "t_battery_id": "Batterie-<PERSON>", "t_battery_type": "Batterietyp", "t_nominal_capacity": "Nennkapazität", "t_real_capacity": "Realkapazität", "t_battery_age": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_duration": "<PERSON><PERSON>", "t_total_mileage": "Gesamtlaufleistung", "t_installation_date": "Einbaudatum", "t_removal_date": "Ausbaudatum", "t_soh": "SOH", "t_safety_score": "Sicherheitsbewertung", "t_safety_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t_safety_score_overview": "Safety Score", "t_battery_safety_evaluation": "Batterie-Sicherheitsbewertung", "t_battery_status": "<PERSON><PERSON><PERSON><PERSON>", "t_battery_issues": "Batterieprobleme", "t_warnings": "Warnungen", "t_current_issues": "Aktuelle Probleme", "t_battery_issue_evaluation": "Batterie-Problem-Bewertung", "t_battery_issue_details": "Problem-Details", "t_battery_history": "<PERSON><PERSON>ie", "t_battery_history_details": "Batteriedetails", "t_battery_history_columns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_current": "aktuell", "t_bm": "Batterie-Monitor", "t_bm_dashboard": "Dashboard", "t_box": "<PERSON><PERSON>", "t_button_label_sign_out": "Abmelden", "t_c2c": "C2C", "t_c2c_count": "C2C", "t_cancel": "Abbrechen", "t_cdda": "LFZ-Analyse", "t_cdda_categories": "Bewertungskategorien", "t_cdda_category_charging": "Laden", "t_cdda_category_defect_tickets": "Mängelmeldungen", "t_cdda_category_delivery": "<PERSON><PERSON><PERSON><PERSON>", "t_cdda_category_drivers_log_entry": "Fahrtenbucheintrag", "t_cdda_category_driving": "Fahren", "t_cdda_category_errors": "<PERSON><PERSON>", "t_cdda_charge_completed": "Charge Completed", "t_cdda_charge_completed_timestamp": "Charge Completed Zeitstempel", "t_cdda_charged_completed_false": "<PERSON>cht erreicht", "t_cdda_charged_completed_true": "E<PERSON><PERSON>t um", "t_cdda_charging_soc_rise": "SOC-Hub in %", "t_cdda_charging_soc_start": "Start-SOC in %", "t_cdda_charging_station": "Ladesäule", "t_cdda_condition": "Bedingung", "t_cdda_connectivity": "Konnektivität", "t_cdda_date_order": "Datumsreihenfolge", "t_cdda_defect_tickets_count": "<PERSON><PERSON><PERSON>ngelmeldungen", "t_cdda_definition_charging": "Definition Laden", "t_cdda_definition_delivery": "Definition Zustellen", "t_cdda_definition_driving": "Definition Fahren", "t_cdda_delivery_distance": "Strecke in km", "t_cdda_delivery_distance_description": "Das Fahrzeug muss mindestens die parametrisierte Strecke zurücklegen damit 'Zustellen' erkannt wird", "t_cdda_delivery_drivers_log_entry": "Fahrtenbucheintrag", "t_cdda_delivery_drivers_log_entry_description": "Es muss ein Fahrtenbucheintrag vorliegen, damit 'Zustellung' erkannt wird", "t_cdda_delivery_stops": "Stopps", "t_cdda_delivery_stops_definition": "Definition Stopp: Geschwindigkeit < 2,5km/h für eine Dauer > 30s", "t_cdda_delivery_stops_description": "Es müssen mindestens die parametrisierte Anzahl an Stopps vorliegen, damit 'Zustellung' erkannt wird", "t_cdda_departure_time_llw": "Abfahrtszeit Ladeleitwarte", "t_cdda_displayed_days": "Angezeigte Tage", "t_cdda_distance": "<PERSON><PERSON><PERSON>", "t_cdda_distance_to_organisational_unit_end": "Abstand zu Organisationseinheit nach Zustellung (FMM / LLW)", "t_cdda_distance_to_organisational_unit_llw_start": "Abstand zu Organisationseinheit (LLW) vor Zustellung", "t_cdda_distance_to_organisational_unit_start": "Abstand zu Organisationseinheit vor Zustellung (FMM / LLW)", "t_cdda_door_open_count": "<PERSON><PERSON><PERSON>n", "t_cdda_drivers_log_entry": "Fahrtenbucheintrag", "t_cdda_driving_distance": "Strecke in km", "t_cdda_driving_distance_description": "Das Fahrzeug muss mindestens die parametrisierte Strecke zurücklegen damit 'Fahren' erkannt wird", "t_cdda_energy_consumption_hint": "Energieverbrauch kann für sehr kurze (Teil-) Strecken ungenau sein", "t_cdda_energy_consumption_per_100_km": "Energieverbrauch pro 100 km", "t_cdda_energy_consumption_total": "Energieverbrauch gesamt", "t_cdda_errors_count": "<PERSON><PERSON><PERSON>", "t_cdda_evaluation": "Bewertung", "t_cdda_ignition_count": "<PERSON><PERSON><PERSON> Zündungswechsel", "t_cdda_mileage_end": "Km-Ende", "t_cdda_mileage_start": "Km-Start", "t_cdda_organisational_unit": "Organisationseinheit (FMM / LLW)", "t_cdda_organisational_unit_llw": "Organisationseinheit (LLW)", "t_cdda_preconditioning_timestamp_received": "Vorkonditionierung empfangen", "t_cdda_preconditioning_timestamp_sended": "Vorkonditionierung gesendet", "t_cdda_soc_charge": "SOC-Hub über Nacht", "t_cdda_soc_consumption": "SOC-Verbrauch", "t_cdda_soc_end": "SOC nach Zustellung", "t_cdda_soc_rise_description": "Es muss mindestens der parametrisierte SOC-<PERSON>b vorliegen, damit '<PERSON><PERSON>' er<PERSON> wird", "t_cdda_soc_start": "SOC vor Zustellung", "t_cdda_soc_start_description": "Es muss mindestens der parametrisierte Start-SOC erreicht werden, damit '<PERSON><PERSON>' erkannt wird", "t_cdda_stops": "<PERSON><PERSON><PERSON>", "t_cdda_telematic_control_unit": "Telematik-Einheit", "t_cdda_temperature_ambient_max": "Max. Umgebungstemperatur", "t_cdda_temperature_ambient_min": "Min. Um<PERSON>bungstemperatur", "t_cdda_temperature_ambient_range": "Außentemperatur Bereich", "t_cdda_total_range": "Gesamter Zeit<PERSON>um", "t_chargeplug": "Ladestecker", "t_charging_information_histogram": "Ladeinformationen", "t_charging_information_histogram_description_x_axis": "X-Achse: Zeiteinheit (Tag, Woche)", "t_charging_information_histogram_description_y_axis": "Y-Achse: Min. bzw. Max<PERSON> (in %) / Zurückgelegte Strecke (in km) pro Zeiteinheit ", "t_charging_station": "Ladepunkt", "t_charging_station_assigned": "Ladepunkt zugewiesen", "t_charging_station_unassigned": "<PERSON><PERSON> zugewiesen", "t_click_on_chart_to_disable_zoom": "Klick auf Chart deaktiviert Zoom", "t_click_on_chart_to_enable_zoom": "Klick auf Chart aktiviert Zoom", "t_close": "Schließen", "t_cm": "Lademonitor", "t_cm_case": "Fall", "t_cm_cases": "<PERSON><PERSON><PERSON>", "t_cm_charge_completed": "Charge Completed", "t_cm_charge_completed_not_reached": "<PERSON>cht erreicht", "t_cm_charge_completed_reached": "Erreicht am", "t_cm_charge_completed_timestamp": "Charge Completed Zeitstempel", "t_cm_charge_status": "Laden i.O.", "t_cm_charge_status_definition": "Ladenstatus >= {{ parameter }} %", "t_cm_charge_status_description": "Der Ladestatus der Telematik-Einheit wird alle 30 Minuten überprüft (Zustände: Laden i.O. / Laden n.i.O.). Der Prozent-Wert ergibt sich aus dem Verhältnis der überprüften Datenpunkte mit dem Zustand Laden i.O. und allen erfassten Datenpunkten.", "t_cm_charge_status_did_not_perform_as_intended": "Laden ist nicht bestimmungsgemäß erfolgt", "t_cm_charge_status_performed_as_intended": "Laden ist bestimmungsgemäß erfolgt", "t_cm_charging_management": "Lademanagement", "t_cm_charging_management_llw": "Ladeleitwarte", "t_cm_charging_management_lulm": "Lade<PERSON> und Last-Management", "t_cm_charging_station": "Ladepunkt", "t_cm_charging_station_valid": "Ladepunktzuweisung", "t_cm_cluster": "Cluster", "t_cm_connectivity": "Konnektivität", "t_cm_connectivity_definition": "Konnektivität >= {{ parameter }} %", "t_cm_dashboard": "Dashboard", "t_cm_dashboard_vehicles_as_testing_by_llw": "Fahrzeuge mit einer Testing/Fleet-Pool-Zuweisung in der LLW", "t_cm_dashboard_vehicles_at_post_location_by_llw": "Fahrzeuge mit einer Post-Standort-Zuweisung in der LLW", "t_cm_dashboard_vehicles_at_production_location_by_llw": "Fahrzeuge mit einer Produktionsstandort-Zuweisung in der LLW", "t_cm_date_order": "Sortierung nach Datum", "t_cm_departure_time_llw": "Abfahrtszeit Ladeleitwarte", "t_cm_displayed_days": "Angezeigte Tage", "t_cm_distance_to_assigned_organisational_unit_fmm_llw": "Abstand zur zugewiesenen Org-Einheit (FMM / LLW)", "t_cm_distance_to_assigned_organisational_unit_valid": "Standort i.O.", "t_cm_distance_to_nearest_organisational_unit_fmm_llw": "Abstand zur nächsten Org-Einheit (FMM / LLW)", "t_cm_evaluation_range": "Von 12:00 (<PERSON><PERSON><PERSON>) bis 11:59 Uhr", "t_cm_fleet": "Flottenansicht", "t_cm_fleet_assigned_organisational_unit_fmm": "Organisationseinheit FMM", "t_cm_fleet_assigned_organisational_unit_llw": "Organisationseinheit LLW", "t_cm_fleet_distance_to_assigned_organisational_unit_fmm": "Abstand zur zugewiesen Org.-Einheit FMM", "t_cm_fleet_distance_to_assigned_organisational_unit_llw": "Abstand zur zugewiesen Org.-Einheit LLW", "t_cm_fleet_distance_to_nearest_organisational_unit_fmm": "Abstand zur nähesten Org.-Einheit FMM", "t_cm_fleet_distance_to_nearest_organisational_unit_llw": "Abstand zur nähesten Org.-Einheit LLW", "t_cm_fleet_temperature_range": "Außentemperatur Bereich", "t_cm_fleet_vehicle_is_assigned_to_charging_station": "Fahrzeug ist einem Ladepunkt zugewiesen", "t_cm_last_sign_of_life_signal": "Letztes Lebenszeichen - Signal", "t_cm_last_sign_of_life_timestamp": "Letztes Lebenszeichen - Zeitstempel", "t_cm_location_check": "Standort i.O.", "t_cm_no_evaluation_available": "<PERSON>s liegt keine Auswertung vor", "t_cm_organisational_unit_fmm": "Organisationseinheit (FMM)", "t_cm_organisational_unit_llw": "Organisationseinheit (LLW)", "t_cm_organisational_unit_type": "Standort-Typ", "t_cm_parameters": "Parameter", "t_cm_soc_charge": "!MISSING_KEY!", "t_cm_soc_charged": "SOC Geladen", "t_cm_soc_end": "SOC-Ende", "t_cm_soc_range": "SOC (Start / Ende)", "t_cm_soc_start": "SOC-Start", "t_cm_version": "Lademonitor-Version", "t_cm_wiki": "Wiki", "t_cm_wiki_analysis": "Analyse", "t_cm_wiki_assign_charging_station_in_llw": "Zuweisung Ladesäule/Prüfung in LLW", "t_cm_wiki_assign_to_charging_station": "Ladepunkt zuweisen", "t_cm_wiki_charge_preset_depends_on_database_entry": "Ladevorgabe in Abhängigkeit von der in der Datenbank abgelegten max. Ladeleistung (0-11 kWh)", "t_cm_wiki_charging_capacity_fallback": "Rückfallladeleistung", "t_cm_wiki_charging_capacity_max": "<PERSON><PERSON>", "t_cm_wiki_current_value": "Aktueller Wert", "t_cm_wiki_description": "Beschreibung", "t_cm_wiki_dpdhl": "DPDHL", "t_cm_wiki_expected_values": "Erwartungswerte", "t_cm_wiki_expected_values_method_post": "Der Abstand des Fahrzeugs zum zugewiesenen Post-Standort wird durch Abgleich des GPS-Signals vom Fahrzeug (tatsächlicher Standort zum Zeitpunkt t) mit den in der LLW hinterlegten Längen-und Breitengraden des Post-Standorts berechnet.", "t_cm_wiki_expected_values_method_production": "Der Abstand des Fahrzeugs zum zugewiesenen Produktionsstandort wird durch Abgleich des GPS-Signals vom Fahrzeug (tatsächlicher Standort zum Zeitpunkt t) mit den in der LLW hinterlegten Längen-und Breitengraden des Produktionsstandorts berechnet. Das Fahrzeug lädt ungeregelt und ein Ladepunkt kann nicht zugewiesen werden.", "t_cm_wiki_expected_values_method_test_vehicles": "Das Laden erfolgt bei Fahrzeugen, die in der LLW als 'Testfahrzeuge' appliziert sind ungeregelt, d.h. mit voller Ladeleistung. Ein Ladepunkt kann nicht zugewiesen werden", "t_cm_wiki_location_distance": "Abstand zum Standort <= {{ parameter }} Meter", "t_cm_wiki_location_post": "Post-Standort", "t_cm_wiki_location_production": "Produktions-Standort", "t_cm_wiki_location_test_vehicle": "Testfahrzeuge", "t_cm_wiki_method": "Verfahren", "t_cm_wiki_not_ok": "n.i.O.", "t_cm_wiki_ok": "i.O.", "t_cm_wiki_related_to_expected_values": "Bezogen auf die Erwartungswerte", "t_cm_wiki_related_to_target_location_in_llw": "Bezogen auf den Soll-Standort gem. LLW", "t_cm_wiki_responsible": "Wer muss was tun (Prämisse: Standort in LLW korrekt)", "t_cm_wiki_return_to_avantis": "Zurückführen zu Avantis", "t_cm_wiki_return_to_avantis_and_analyse": "Zurückführen zu Avantis & Analyse", "t_cm_wiki_return_to_llw_location": "Fahrzeug an zugewiesenen Standort in LLW zurückführen", "t_cm_wiki_return_to_production_location": "Zurückführen an den Produktionsstandort", "t_cm_wiki_return_to_production_location_and_analyse": "Zurückführen an den Produktionsstandort & Analyse", "t_cm_wiki_sts": "StS", "t_cm_wiki_task": "Aufgabe", "t_cm_wiki_vehicle_close_to_llw": "Fzg. nahe des Standorts LLW", "t_cm_wiki_vehicle_far_away_from_llw": "Fahrzeug nicht nahe des Standorts gem. LLW; Pampa oder Post-Standort", "t_cm_wiki_vehicle_far_away_from_llw_pampa": "Fern des Standorts gem. LLW; Pam<PERSON>", "t_cm_wiki_vehicle_far_away_from_llw_post": "Fern des Standorts gem. LLW; aber Post-Standort", "t_cm_wiki_vehicle_location_description": "Beschreibung zum Fzg.-Standort", "t_color_code": "Farbcode", "t_configuration": "Konfiguration", "t_configuration_properties": "Konfigurationseigenschaften", "t_configurations": "<PERSON><PERSON><PERSON><PERSON>", "t_connected": "Verbunden", "t_connectivity": "Konnektivität", "t_connectivity_description": "Die Konnektivität der Telematik-Einheit wird alle 15 Minuten überprüft (Zustände: Konnektivität vorhanden / Konnektivität nicht vorhanden). Der Prozent-Wert ergibt sich aus dem Verhältnis der überprüften Datenpunkte mit dem Zustand Konnektivität vorhanden und allen erfassten Datenpunkten.", "t_connectivity_histogram_x_axis_description": "X-Achse: Zeiteinheit (Tag, Woche)", "t_connectivity_histogram_y_axis_description": "Y-Achse: An<PERSON>il (in %) eines TCU-Zustands pro Zeiteinheit", "t_content": "Inhalt", "t_continue": "<PERSON><PERSON>", "t_cookies": "Cookies", "t_count": "<PERSON><PERSON><PERSON>", "t_count_stops": "<PERSON><PERSON><PERSON>", "t_covered_distance": "Zurückgelegte Strecke", "t_create_report": "Report erstellen", "t_current_hardware": "Hardwarestand", "t_current_position_global": "Aktuelle Position Global", "t_current_position_local": "Aktuelle Position Lokal", "t_current_software": "Softwarestand", "t_currently": "Aktuell", "t_cw": "!MISSING_KEY!", "t_daily": "Tä<PERSON><PERSON>", "t_dashboard": "Dashboard", "t_dashboards": "Dashboards", "t_data": "Daten", "t_data_quality": "Datenqualität", "t_data_quality_good": "Datenqualität gut", "t_data_quality_good_description": "TCU / OLU", "t_data_quality_no_data": "<PERSON>s liegen wahrscheinlich keine Daten vor", "t_data_quality_no_data_description": "<PERSON><PERSON> Telematik-Einheit", "t_data_quality_restricted": "Datenqualität eingeschränkt", "t_data_quality_restricted_description": "C2C", "t_date": "Datum", "t_day": "Tag", "t_day_view": "Tagesansicht", "t_days": "Tage", "t_days_10": "10 Tage", "t_days_14": "14 Tage", "t_days_7": "7 Tage", "t_days_unusable": "Ausfalltage", "t_defect_tickets": "Mängel-Meldungen", "t_defect_tickets_can_drive": "Fahrbereit", "t_defect_tickets_can_roll": "Rollbereit", "t_defect_tickets_date_repair_planned": "<PERSON><PERSON> g<PERSON>", "t_defect_tickets_defect_tickets_count": "Meldungen gesamt", "t_defect_tickets_defect_tickets_open_count": "Meldungen offen", "t_defect_tickets_details": "Details", "t_defect_tickets_error_codes": "<PERSON><PERSON>-Codes", "t_defect_tickets_error_description": "Fehlerbeschreibung", "t_defect_tickets_id": "Mängelmeldung", "t_defect_tickets_is_accident": "Ist Unfall", "t_defect_tickets_process_id": "Prozessnummer", "t_defect_tickets_remarks": "Bemerkungen", "t_defect_tickets_replacement_required": "<PERSON><PERSON><PERSON><PERSON>", "t_defect_tickets_status": "Status", "t_defect_tickets_status_cancelled": "<PERSON><PERSON><PERSON><PERSON>", "t_defect_tickets_status_closed": "Geschlossen", "t_defect_tickets_status_open": "<PERSON>en", "t_defect_tickets_timestamp_report": "Zeitstempel", "t_defective_vehicles_by_days": "Fahrzeuge nach Ausfalldauer am Stichtag", "t_defects": "Meldungen", "t_delete": "Löschen", "t_delete_item_description": "Sie sind dabei den gewählten Eintrag zu löschen.", "t_delete_item_title": "Wollen Sie diesen Eintrag wirklich löschen?", "t_delivery_trip": "Zustellfahrt", "t_delivery_trip_description": "Zustellfahrt vorhanden. Datenquelle: Verfügbarkeitsreport", "t_delivery_trips": "Zustellfahrten", "t_delivery_trips_histogram_x_axis_description": "X-Achse: Zeiteinheit (Tag, Woche)", "t_delivery_trips_histogram_y_axis_description": "Y-Achse: Zustellfahrt bzw. keine Zustellfahrt vorhanden pro Zeiteinheit", "t_descend": "Absteigend", "t_description": "Beschreibung", "t_diagnostic_procotol_not_found": "Diagnose-Protokoll nicht gefunden", "t_diagnostic_sessions": "Diagnosesitzungen", "t_diagnostics": "Fahrzeugdiagnose", "t_diesel": "Diesel", "t_disconnected": "Getrennt", "t_distance": "<PERSON><PERSON><PERSON>", "t_distance_description": "Zurückgelegte Strecke pro Zeiteinheit. Datenquelle: Tägliche Statistiken", "t_distance_drivers_log": "Streck<PERSON> Fahrtenbuch", "t_distance_drivers_log_description": "Zurückgelegte Strecke pro Zeiteinheit. Datenquelle: Fahrtenbuch-App", "t_distance_telematic_control_unit": "Strecke Telematik-Einheit", "t_distance_telematic_control_unit_description": "Zurückgelegte Strecke pro Zeiteinheit. Datenquelle: Tägliche Statistiken", "t_do_you_want_to_continue": "Fortfahren?", "t_download": "Download", "t_download_diagnostic_procotol_failed": "Download fehlgeschlagen", "t_download_diagnostic_procotol_succeded": "Download erfolgreich", "t_download_diagnostic_protocol": "<PERSON><PERSON><PERSON>", "t_download_excel": "Download Excel", "t_download_image": "Download Bild", "t_downloading_data": "Daten werden geladen ...", "t_drive_readyness_histogram": "Fahrtüchtigkeit", "t_drivemode": "Fahrmodus", "t_drivemode_drive": "Drive", "t_drivemode_eco": "Eco", "t_drivemode_neutral": "Neutral", "t_drivemode_reverse": "Reverse", "t_driven_mileage_and_stops": "Gefahrene <PERSON>lometer und Stops", "t_drivers_log": "Fahrtenbuch", "t_drivers_log_details": "Fahrtdetails", "t_drivers_log_distance": "<PERSON><PERSON><PERSON>", "t_drivers_log_door_open_count": "<PERSON><PERSON><PERSON>n", "t_drivers_log_energy_consumed_per_100_km": "Verbrauchte Energie pro 100 km", "t_drivers_log_energy_consumed_total": "Verbrauchte Energie", "t_drivers_log_gps_end": "Fahrtende", "t_drivers_log_gps_start": "Fahrtbeginn", "t_drivers_log_mileage_end": "KM-Stand Ende", "t_drivers_log_mileage_start": "KM-Stand Start", "t_drivers_log_soc_end": "SOC Ende", "t_drivers_log_soc_start": "SOC Start", "t_drivers_log_stops": "<PERSON><PERSON><PERSON>", "t_drivers_log_timestamp_end": "Zeitstempel Ende", "t_drivers_log_timestamp_start": "Zeitstempel Start", "t_driving_behavior_histogram_x_axis_description": "X-Achse: Zeiteinheit (Tag, Woche)", "t_driving_behavior_histogram_y_description": "Y-Achse: Zurückgelegte Strecke (in km) und Anzahl Stops pro Zeiteinheit", "t_driving_time": "Fahrzeit", "t_driving_vs_standing_time": "Fahrzeit vs. Standzeit", "t_ecu": "Steuergerät", "t_ecu_hardware_status": "Steuergeräte Hardware", "t_ecu_software_status": "Steuergeräte Software", "t_edit": "<PERSON><PERSON><PERSON>", "t_edit_item": "Eintrag bearbeiten", "t_eintracht": "Eintracht", "t_electric": "Elektrisch", "t_empty_list": "Die Liste ist leer", "t_end": "<PERSON><PERSON>", "t_energy_monitor": "Vebrauch<PERSON>ni<PERSON>", "t_energy_monitor_charging_station": "Ladepunkt (Anfang / Ende)", "t_energy_monitor_charging_station_first": "Ladestation Anfang", "t_energy_monitor_charging_station_last": "Ladestation Ende", "t_energy_monitor_connectivity": "Konnektivität", "t_energy_monitor_date_order": "Datumsreihenfolge", "t_energy_monitor_displayed_weeks": "Angezeigte W<PERSON>en", "t_energy_monitor_distance": "Zurückgelegte Strecke", "t_energy_monitor_efficiency_charging": "Wirkungsgrad Laden", "t_energy_monitor_energy_charged_ac": "Energie Geladen AC (Gesamt / pro 100km)", "t_energy_monitor_energy_charged_dc": "Energie Geladen DC (Gesamt / pro 100km)", "t_energy_monitor_energy_consumed": "Energie Verbraucht (Gesamt / pro 100km)", "t_energy_monitor_mileage_end": "KM-Stand Anfang", "t_energy_monitor_mileage_start": "KM-Stand Ende", "t_energy_monitor_organisational_unit": "Organisationseinheit (Anfang / Ende)", "t_energy_monitor_organisational_unit_first": "Organisationseinheit Anfang", "t_energy_monitor_organisational_unit_last": "Organisationseinheit Ende", "t_energy_monitor_soc_charged": "SOC Geladen", "t_energy_monitor_soc_consumed": "SOC Verbraucht", "t_energy_monitor_telematic_control_unit": "Telematik-Einheit (Anfang / Ende)", "t_energy_monitor_telematic_control_unit_first": "Telematik-Einheit Anfang", "t_energy_monitor_telematic_control_unit_last": "Telematik-Einheit Ende", "t_energy_monitor_temperature_max": "Außentemperatur Max", "t_energy_monitor_temperature_median": "Außentemperatur Median", "t_energy_monitor_temperature_min": "Außentemperatur Min", "t_energy_monitor_temperature_range": "Außentemperatur Bereich", "t_energy_monitor_total_range": "Gesamter Zeit<PERSON>um", "t_engineering": "Engineering", "t_engineering_diagnostics": "Diagnose", "t_engineering_errors": "<PERSON><PERSON>", "t_engineering_signals": "Signale", "t_english": "<PERSON><PERSON><PERSON>", "t_error": "<PERSON><PERSON>", "t_error_1": "12V-<PERSON><PERSON><PERSON>er SOC", "t_error_101": "GPS defekt", "t_error_101_description": "TBD", "t_error_102": "CAN defekt", "t_error_102_description": "TBD", "t_error_103": "Vorkonditionierung defekt", "t_error_103_description": "TBD", "t_error_1_description": "TBD", "t_error_2": "Motorwarnleuchte", "t_error_2_description": "TBD", "t_error_3": "<PERSON><PERSON><PERSON><PERSON>", "t_error_3_description": "TBD", "t_error_4": "Ladestecker eingesteckt keine Spannung", "t_error_4_description": "TBD", "t_error_5": "HV-Batterie überhitzt", "t_error_5_description": "TBD", "t_error_6": "EmmcNotMounted", "t_error_6_description": "TDB", "t_error_categories_description": "Beschreibung Fehlerkategorien", "t_error_code": "Fehlercode", "t_error_distribution": "Fehlerverteilung", "t_error_template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t_errors": "<PERSON><PERSON>", "t_errors_count": "<PERSON><PERSON><PERSON>", "t_errors_error_type": "<PERSON><PERSON>", "t_errors_error_type_1": "12V-<PERSON><PERSON><PERSON>er SOC", "t_errors_error_type_101": "GPS defekt", "t_errors_error_type_102": "CAN defekt", "t_errors_error_type_103": "Vorkonditionierung defekt", "t_errors_error_type_2": "Motorwarnleuchte", "t_errors_error_type_3": "<PERSON><PERSON><PERSON><PERSON>", "t_errors_error_type_4": "Ladestecker eingesteckt keine Spannung", "t_errors_error_type_5": "HV-Batterie überhitzt", "t_errors_error_type_6": "EmmcNotMounted", "t_errors_histogram": "Fehler im Zeitverlauf", "t_errors_timestamp": "Zeitstempel", "t_existing_data_available": "Bestandsdaten verfügbar", "t_expired": "Abgelaufen", "t_export": "Export", "t_export_csv": "CSV Export", "t_export_excel": "Excel Export", "t_feature_gps_data": "Zugriff auf GPS-Daten", "t_filter": "Filter", "t_filter_view": "Ansicht filtern", "t_financial_compliant": "Finanzamt-konform", "t_finished": "<PERSON>den", "t_fleet": "<PERSON><PERSON><PERSON>", "t_fleet_average": "Flottendurchschnitt", "t_fleet_configuration": "Flottenkonfiguration", "t_fleet_configuration_basic": "Basiskonfiguration", "t_fleet_configuration_edit_fleet": "<PERSON><PERSON><PERSON> bearbeiten", "t_fleet_configuration_import_vehicles_via_csv": "Fahrzeuge über CSV-Upload importieren", "t_fleet_configuration_mode_description": "Beschreibung", "t_fleet_configuration_mode_organisational_units": "Option 2: Konfiguration via Organisationseinheiten", "t_fleet_configuration_mode_organisational_units_description": "Die Flotte wird auf Basis von Organisationseinheiten erstellt. Die betrachteten Fahrzeuge ergeben sich dabei zu jedem Zeitpunkt aus dem Fahrzeugbestand der ausgewählten Organisationseinheiten.", "t_fleet_configuration_mode_organisational_units_description_details": "Wird eine Flotte aus Basis von Organisationseinheiten erstellt verändern sich die betrachteten Fahrzeuge im Zeitverlauf, sobald sich der Fahrzeugbestand der ausgewählten Organisationseinheiten verändert.", "t_fleet_configuration_mode_vehicles": "Option 1: Konfiguration via Fahrzeugsliste", "t_fleet_configuration_mode_vehicles_description": "Die Flotte wird auf Fahrzeugbasis erstellt. Dabei werden zu jedem Zeitpunkt die ausgewählten Fahrzeuge betrachtet, unabhängig davon welchen Organisationseinheiten diese zugewiesen sind.", "t_fleet_configuration_mode_vehicles_description_details": "Wird eine Flotte auf Fahrzeugsbasis erstellt bleiben die betrachteten Fahrzeuge zu jedem Zeitpunkt gleich, auch wenn die Zuweisung der Fahrzeuge zu ihren Organisationseinheiten ändert.", "t_fleet_configuration_submit_deleted": "<PERSON><PERSON><PERSON> wurde erfolgreich <PERSON>", "t_fleet_configuration_submit_description": "Sie können auf dieser Seite weitermachen oder die Flottenkonfiguration beenden.", "t_fleet_configuration_submit_unsubscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von F<PERSON> abgemeldet", "t_fleet_configuration_submit_unsubscribe_description": "Sie können auf dieser Seite weitermachen oder die Flottenkonfiguration beenden.", "t_fleet_shared_unsubscribe_button_text": "<PERSON> geteilter Flotte abmelden", "t_fleet_configuration_vehicles_count": "Aktueller Fahrzeugbestand der gewählten Organisationseinheiten:", "t_fleet_configuraton_new_fleet": "Neue Flotte erstellen", "t_fleet_create": "Neue Flotte", "t_fleet_default_description": "Die Default-Flotte nicht bearbeitet werden.", "t_fleet_default_title": "Default-Flotte", "t_fleet_delete": "<PERSON><PERSON><PERSON>", "t_fleet_delete_confirm": "Soll die gewählte Flotte wirklich gelöscht werden?", "t_fleet_edit": "<PERSON><PERSON><PERSON> bearbeiten", "t_fleet_name": "Name", "t_fleet_organisational_units": "Organisationseinheiten", "t_fleet_parameters": "Flottenparameter", "t_fleet_parameters_switch_description": "Parameter aktivieren / deaktivieren", "t_fleet_selected": "Ausgewählte Flotte", "t_fleet_shared_description": "Geteilte Flotte können nicht bearbeitet werden.", "t_fleet_shared_title": "Geteilte Flotte", "t_fleet_shared_users": "<PERSON><PERSON><PERSON>", "t_fleet_sharing": "Freigabe", "t_fleet_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_fleet_status_chargeplug_status": "Ladestecker", "t_fleet_status_chargeplug_status_plugged": "Ladestecker eingesteckt", "t_fleet_status_chargeplug_status_timestamp": "Ladestecker Zeitstempel", "t_fleet_status_chargeplug_status_unplugged": "Ladestecker getrennt", "t_fleet_status_charging_current": "<PERSON><PERSON><PERSON>", "t_fleet_status_charging_current_timestamp": "Ladestecker Zeitstempel", "t_fleet_status_details": "Fahrzeugdetails", "t_fleet_status_ignition_status": "Zündung", "t_fleet_status_ignition_status_off": "Zündung ausgeschaltet", "t_fleet_status_ignition_status_on": "Zündung eingeschaltet", "t_fleet_status_ignition_status_timestamp": "Zündung Zeitstempel", "t_fleet_status_mileage": "KM-Stand", "t_fleet_status_online_status": "Online-Status", "t_fleet_status_online_status_online": "Fahrzeug ist online", "t_fleet_status_online_status_timestamp": "Online-Status Zeitstempel", "t_fleet_status_soc": "Ladezustand", "t_fleet_status_soc_timestamp": "Ladezustand Zeitstempel", "t_fleet_status_vehicle_activity": "<PERSON>utz<PERSON>", "t_fleet_status_vehicle_activity_active": "Fahrzeug in Benutzung", "t_fleet_status_vehicle_activity_description": "Fahrzeuge werden als in Benutzung angenommen, wenn sie in den letzten 30 Minuten eine aktive Zündung oder eine Erhöhung des Kilometerstandes gesendet haben", "t_fleet_status_vehicle_activity_inactive": "Fahrzeug nicht in Benutzung", "t_fleet_vehicles": "Fahrzeuge", "t_fleet_view": "Flottenansicht", "t_fpp": "Feldpflegepaket", "t_friday": "Freitags", "t_fridays": "Freitage", "t_gasoline": "Benzin", "t_german": "De<PERSON>ch", "t_hardware_change_required": "Überprüfung notwendig", "t_hardware_version": "Hardwarestand", "t_hardware_version_ok": "Version in Ordnung", "t_header_user_settings": "Benutzereinstellungen", "t_help": "<PERSON><PERSON><PERSON>", "t_holidays": "Bundesweite Feiertage", "t_hour": "Stunde", "t_http_error_400": "Bad Request", "t_http_error_401": "Unauthorized", "t_http_error_404": "Not Found", "t_http_error_500": "Server Error", "t_http_error_client_error": "<PERSON><PERSON>", "t_http_error_connection_refused": "<PERSON><PERSON>zwerkverbindung", "t_http_error_occurred": "Ein Fehler ist aufgetreten", "t_http_error_occurred_sorry": "Entschuldigung", "t_http_error_placeholder": "<PERSON><PERSON>", "t_http_error_placeholder_description": "Bitte versuchen Si<PERSON> es zu einem späteren Zeitpunkt noch einmal.", "t_http_error_undefined": "Unbekannter Fehler", "t_iac_mains_bool": "<PERSON><PERSON><PERSON>", "t_ignition_status": "Zündung", "t_imprint": "Impressum", "t_in_depot": "Im Depot", "t_in_garage": "In d. Werkstatt", "t_inactive": "Inaktiv", "t_is_loading": "Wird geladen ...", "t_is_required": "darf nicht leer sein", "t_is_updating": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "t_item": "Eintrag", "t_items": "Einträge", "t_japanese": "Japanisch", "t_last_days": "Letzte {{count}} Tage", "t_last_in_depot": "Zuletzt in Depot", "t_last_in_garage": "Zuletzt in d. Werkstatt", "t_last_of_life_never": "<PERSON><PERSON>", "t_last_sign_of_life": "Letztes Lebenszeichen", "t_last_sign_of_life_signal": "Letztes Lebenszeichen Signal", "t_last_sign_of_life_timestamp": "Letztes Lebenszeichen Zeitpunkt", "t_last_week": "Letzte Woche", "t_license_plate": "Kennzeichen", "t_license_plate_missing": "Nicht vorhanden", "t_loading": "Lade", "t_log_data_request_not_possible": "<PERSON><PERSON>", "t_log_data_request_possible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_manufacturer": "<PERSON><PERSON><PERSON>", "t_manufacturer_abt_e_line": "ABT e-Line", "t_manufacturer_audi": "Audi", "t_manufacturer_bmw": "BMW", "t_manufacturer_bon": "B-ON", "t_manufacturer_daimler": "Daimler", "t_manufacturer_evolution": "eVolution", "t_manufacturer_ford": "Ford", "t_manufacturer_iveco": "IVECO", "t_manufacturer_posche_e": "Posche E", "t_manufacturer_streetscooter": "Streetscooter", "t_manufacturer_streetscooter_engineering": "Streetscooter Engineering", "t_manufacturer_volkswagen": "Volkswagen", "t_manufacturer_mercedes_benz": "Mercedes-Benz", "t_manufacturers": "<PERSON><PERSON><PERSON>", "t_meter": "<PERSON>er", "t_mileage": "Kilometerstand", "t_minute": "Minute", "t_missing_name_message": "<PERSON>s muss ein <PERSON> vergeben werden", "t_model": "<PERSON><PERSON>", "t_model_work": "Work", "t_model_work_l": "Work L", "t_model_work_xl": "Work XL", "t_model_evito": "eVito", "t_model_eabt": "eAbt", "t_model_max": "Max", "t_module_cdda": "LFZ-Analyse", "t_module_cm": "Lademonitor", "t_module_cm_extended": "La<PERSON><PERSON><PERSON>", "t_module_defect_tickets": "Mängel-Meldungen", "t_module_energy_monitor": "Verbrauchsmonitor", "t_module_engineering": "Engineering", "t_module_engineering_extended": "Engineering Erweitert", "t_module_settings_base_columns": "Basis-Spalten", "t_module_settings_export_columns": "Spalten", "t_module_settings_export_format": "Format", "t_module_settings_export_range": "Zeitraum", "t_module_settings_export_title": "Export", "t_module_settings_fixed_column": "Fixieren bis", "t_module_settings_further_view_settings": "Weitere Einstellungen", "t_module_settings_range": "Beobachtungszeitraum", "t_module_settings_range_description": "Der Beobachtungszeitraum wird unter dem Menü Flottenkonfiguration (siehe Button) konfiguriert", "t_module_settings_configure_view_title": "Ansicht konfigurieren", "t_module_user_management": "Benutzerverwaltung", "t_module_vlv": "Fahrzeug Live-Ansicht", "t_modules": "<PERSON><PERSON><PERSON>", "t_monday": "Montag<PERSON>", "t_mondays": "Montage", "t_month_view": "Monatsansicht", "t_monthly": "<PERSON><PERSON><PERSON>", "t_multi_selector_placeholder_default": "Bitte auswählen", "t_name": "Name", "t_never": "<PERSON><PERSON>", "t_never_online": "Noch nie online", "t_no": "<PERSON><PERSON>", "t_no_data": "<PERSON><PERSON>", "t_no_data_available": "<PERSON>s liegen keine Daten vor", "t_no_defect_tickets": "<PERSON><PERSON>", "t_no_defect_tickets_existing": "Es liegen keine Mängelmeldungen vor", "t_no_delivery_trip": "<PERSON><PERSON>", "t_no_delivery_trip_description": "<PERSON>ine Zustellfahrt vorhanden. Datenquelle: Verfügbarkeitsreport", "t_no_diagnostic_sessions_available": "Es liegen keine Diagnosesitzungen vor", "t_no_errors": "<PERSON><PERSON>", "t_no_errors_existing": "<PERSON>s liegen keine Fehler vor", "t_no_gps_data": "Keine GPS Daten", "t_no_gps_data_available": "<PERSON>s liegen keine GPS Daten vor", "t_no_telematic_unit": "<PERSON><PERSON>ematic-Einheit", "t_no_unit": "<PERSON><PERSON>", "t_no_valid_data_basis": "!MISSING_KEY!", "t_none_count": "<PERSON><PERSON> Telematik-Einheit", "t_not_available": "Nicht verfügbar", "t_number_of_vehicles": "<PERSON><PERSON><PERSON> Fahrzeuge", "t_of": "von", "t_off": "Aus", "t_offline": "Offline", "t_olu": "OLU", "t_olu_count": "OLU", "t_on": "Ein", "t_online": "Online", "t_online_status": "Online-Status", "t_orange": "Orange", "t_org_nl": "NL", "t_org_rgb": "RGB", "t_org_zsp": "ZSP", "t_organisational_unit": "Organisationseinheit", "t_organisational_unit_depot": "<PERSON><PERSON>", "t_organisational_unit_missing": "Nicht vorhanden", "t_other_count": "Sonstige", "t_other_position": "<PERSON><PERSON>", "t_others": "<PERSON><PERSON>", "t_outage_days": "Ausfalltage", "t_pickup": "<PERSON><PERSON><PERSON>", "t_powertrains": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_preconditioning": "Vorkonditionierung empfangen", "t_preconditioning_timestamp": "Vorkonditionierung gesendet um", "t_privacy_policy": "Datenschutzerklärung", "t_property": "Eigenschaft", "t_pure": "<PERSON><PERSON><PERSON>", "t_range_last_day": "Letzter Tag", "t_range_last_four_weeks": "Letzte 4 Wochen", "t_range_last_month": "Letzter Monat", "t_range_last_three_days": "Letzte 3 Tage", "t_range_last_three_weeks": "Letzte 3 Wochen", "t_range_last_two_weeks": "Letzte 2 Wochen", "t_range_last_week": "Letzte Woche", "t_total_throughput": "Throughput", "t_read_csv_file_error": "Fehler beim Lesen der CSV-Datei", "t_refresh": "Aktualisieren", "t_report_deleted": "Report gelöscht", "t_report_name": "Report-Name", "t_reporting_category": "Meldekate<PERSON><PERSON>", "t_reports": "Reports", "t_reset_password_email_sent": "Email für Passwort-Reset gesendet", "t_result_sub_title_no_fleet_selected": "Falls Sie noch keine Flotte konfiguriert haben können Sie über den untenstehenden Button oder den Menüpunkt Flottenkonfiguration eine Flotte neue erstellen.", "t_result_sub_title_no_vehicle_selected": "Über den Selektor können sie ein Fahrzeug aus dem Ihnen zur Verfügung stehenden Fahrzeugbestand auswählen.", "t_result_sub_title_role_expired": "Ihre Subscription ist abgelaufen, bitte kontaktieren Sie Ihren Amdinistrator.", "t_result_title_no_fleet_selected": "Zum Starten bitte eine Flotte auswählen", "t_result_title_no_vehicle_selected": "Zum Starten bitte ein Fahrzeug auswählen", "t_result_title_role_expired": "Subscription abgelaufen", "t_retry": "<PERSON><PERSON><PERSON> versuchen", "t_rhythm": "Rhythmus", "t_rides_count": "<PERSON><PERSON><PERSON>", "t_role": "Subscription", "t_role_create": "Neue Subscription", "t_role_create_success": "Subscription erfolgreich angelegt", "t_role_create_title": "Neue Subscription anlegen", "t_role_deleted": "Subscription erfolgreich gelöscht", "t_role_details": "Details", "t_role_edit": "Subscription bearbeiten", "t_role_edit_success": "Subscription erfolgreich bearbeitet", "t_role_expiration": "Laufzeit", "t_role_expiration_is_required": "Bitte Laufzeit angeben", "t_role_expiration_limited": "<PERSON><PERSON><PERSON><PERSON>", "t_role_expiration_placeholder": "Laufzeit", "t_role_expiration_unlimited": "Unbefristet", "t_role_features": "Zusätzliche Features", "t_role_manufacturers": "<PERSON><PERSON><PERSON>", "t_role_manufacturers_is_required": "Bitte mindestens einen Hersteller hinzufügen", "t_role_missing": "Keine Subscription", "t_role_modules": "<PERSON><PERSON><PERSON>", "t_role_modules_is_required": "Bitte mindestens ein Modul hinzufügen", "t_role_name": "Name", "t_role_name_is_required": "Bitte Name angeben", "t_role_name_placeholder": "Name", "t_role_organisational_units": "Organisationseinheiten", "t_role_organisational_units_is_required": "Bitte mindestens eine Organisationseinheit hinzufügen", "t_role_user_already_has_subscription": "Benutzer gehört bereits zu einer anderen Subscription", "t_role_users": "<PERSON><PERSON><PERSON>", "t_roles": "Subscriptions", "t_saturday": "Samstags", "t_saturdays": "Samstage", "t_save": "Speichern", "t_score": "Score", "t_scoring": "Bewertung", "t_search": "<PERSON><PERSON>", "t_second": "Sekunde", "t_select_configuration": "Konfiguration auswählen", "t_select_days": "Tage auswählen", "t_select_error": "Fehler auswählen", "t_select_fpp_type": "Feldpflegepaket auswählen", "t_select_levels": "Standorte auswählen", "t_select_manufacturer": "Hersteller auswählen", "t_select_monthday": "Tag auswählen", "t_select_organisational_units": "Ogranisationseinheiten", "t_select_powertrain_type": "Antriebsart auswählen", "t_select_recipients": "Empfänger auswählen", "t_select_rhythm": "Rhythmus auswählen", "t_select_rhythm_message": "Bitte Rhythmus angeben", "t_select_sampling_rate": "Abtastrate", "t_select_signals": "Gewählte Signale", "t_select_subfleet_message": "Subfltte auswählen", "t_select_template": "Vorlage auswählen", "t_select_template_message": "Mindestens eine Vorlage auswählen", "t_select_vehicle": "Fahrzeug auswählen", "t_select_vehicles": "Fahrzeuge auswählen", "t_select_weekday": "Wochentag auswählen", "t_selected": "Ausgewählt", "t_selected_organisational_units": "Ausgewählte Organisationseinheiten", "t_selected_users": "Ausgewählte Benutzer", "t_selected_vehicles": "Ausgewählte Fahrzeuge", "t_selector_placeholder_default": "Bitte auswählen", "t_serial_number": "Seriennummer", "t_settings_language": "<PERSON><PERSON><PERSON>", "t_settings_password": "Passwort", "t_settings_reset_password": "Password zurücksetzen", "t_settings_role": "Subscription", "t_settings_role_expiration": "Laufzeit", "t_settings_role_name": "Subscription", "t_settings_select_language": "Sprache auswählen", "t_settings_send_email_for_password_reset": "Email für Passwort-Reset senden", "t_seven_days_ago": "Vor 7 Tagen", "t_signals": "Signale", "t_signals_export": "Export", "t_signals_export_timestamp": "Zeitstempel", "t_signals_log_data_request_successfully_sent": "Daten wurden erfolgreich ange<PERSON>", "t_signals_normalized_ambient_temperature": "Außentemperatur", "t_signals_normalized_bms_status": "BMs-Status", "t_signals_normalized_chargeplug_status": "Ladestecker", "t_signals_normalized_charging_current_bool": "Ladestrom AC (Binär)", "t_signals_normalized_charging_current_dc": "Ladestrom DC", "t_signals_normalized_charging_power_dc_max_derating": "Ladevorgabe Derating", "t_signals_normalized_charging_power_dc_set_offboard": "Ladevorgabe Offboard", "t_signals_normalized_charging_power_dc_set_onboard": "Ladevorgabe OnBoard", "t_signals_normalized_charging_voltage_bool": "Ladespannung AC (Binär)", "t_signals_normalized_charging_voltage_dc": "Ladespannung DC", "t_signals_normalized_gps_latitude": "GPS-Breitengrad", "t_signals_normalized_gps_longitude": "GPS-Längengrad", "t_signals_normalized_ignition_status": "Zündung", "t_signals_normalized_odometer": "Kilometerstand", "t_signals_normalized_online_status": "Online Status", "t_signals_normalized_soc": "Ladezustand", "t_signals_request_data": "<PERSON>n an<PERSON>", "t_signals_show_datapoints": "Datenpunkte anzeigen", "t_signals_view": "Signale anzeigen", "t_soc": "Ladezustand", "t_soc_charging_stats_max": "<PERSON><PERSON>", "t_soc_charging_stats_max_description": "Max. Ladezustand pro Zeiteinheit. Datenquelle: Ladestatistiken", "t_soc_charging_stats_min": "<PERSON><PERSON>", "t_soc_charging_stats_min_description": "Min. Ladezustand pro Zeiteinheit. Datenquelle: Ladestatistiken", "t_soc_delta": "Delta Ladezustand", "t_soc_delta_description": "Delta Ladezustand pro Zeiteinheit. Datenquelle: Signale", "t_soc_in_percent": "Ladezustand in Prozent", "t_soc_max": "<PERSON><PERSON>", "t_soc_max_description": "Max. Ladezustand pro Zeiteinheit. Datenquellen: Signale", "t_soc_min": "<PERSON><PERSON>", "t_soc_min_description": "Min. Ladezustand pro Zeiteinheit. Datenquellen: Signale", "t_soc_normalized_signals_max": "<PERSON><PERSON>", "t_soc_normalized_signals_max_description": "Max. Ladezustand pro Zeiteinheit. Datenquelle: Signale", "t_soc_normalized_signals_min": "<PERSON><PERSON>", "t_soc_normalized_signals_min_description": "Min. Ladezustand pro Zeiteinheit. Datenquelle: Signale", "t_software_update_required": "Überprüfung notwendig", "t_software_version": "Softwarestand", "t_software_version_ok": "Version in Ordnung", "t_speed_avg": "Durch. Geschwindigkeit", "t_speed_max": "Max. Geschwindigkeit", "t_standing_time": "Standzeit", "t_start": "Start", "t_statistics": "Statistiken", "t_status": "Status", "t_status_charging": "Bewertung Laden", "t_status_defects": "Mängelmeldungen", "t_status_delivery": "Bewertung Zustellen", "t_status_driving": "Bewertung Fahren", "t_status_errors": "<PERSON><PERSON>", "t_status_metadata": "Bewertung Metadaten", "t_stops": "Stopps", "t_stops_description": "Anzahl Stops pro Zeiteinheit. Datenquelle: Tägliche Statistiken", "t_streetscooter": "Streetscooter", "t_subfleet": "Subflotte", "t_subfleet_status_invalid": "<PERSON><PERSON><PERSON><PERSON>", "t_subfleet_status_invalid_description": "Eine Organisationseinheit, die zu dieser Subflotte gehört, existiert nicht mehr. Diese Subflotte kann daher nicht mehr verwendet werden.", "t_subfleet_status_read_only": "Schreibgeschützt", "t_subfleet_status_read_only_description": "Die Standard-Subflotte kann nicht veränder<PERSON>, gel<PERSON><PERSON>t oder geteilt werden", "t_subscription": "Subscription", "t_sunday": "Sonntags", "t_sundays": "Sonntage", "t_target_hardware": "Soll-Hardwarestand", "t_target_software": "Soll-Softwarestand", "t_tcu": "TCU", "t_tcu_count": "TCU", "t_tcu_state_active": "Aktiv", "t_tcu_state_active_description": "Die Telematik-Einheit arbeitet bestimmungsgemäß. Datenquelle: Signale", "t_tcu_state_available": "Verfügbar", "t_tcu_state_available_description": "Konnektivität ist vorhanden. Datenquelle: Signale", "t_tcu_state_no_box_assigned": "Keine Box-<PERSON>wi<PERSON>n", "t_tcu_state_no_box_assigned_description": "Dem Fahrzeug wurde keine Telematik-Einheit zugewiesen. Datenquelle: Bestandsdaten", "t_tcu_state_no_gps": "<PERSON>in <PERSON>", "t_tcu_state_no_gps_description": "Die Telematik-Einheit hat eventuell keinen GPS-Empfang. Datenquelle: Signale", "t_tcu_state_no_gsm": "<PERSON>in <PERSON>", "t_tcu_state_no_gsm_description": "Die Telematik-Einheit hat eventuell keinen GSM-Empfang. Datenquelle: Signale", "t_tcu_state_not_available": "Nicht verfügbar", "t_tcu_state_not_available_description": "Konnektivität ist für einen längeren Zeitraum unterbrochen. Datenquelle: Signale", "t_tcu_state_offline": "Offline", "t_tcu_state_offline_description": "Die Telematik-Einheit ist bestimmungsgemäßg offline. Datenquelle: Events", "t_tcu_state_potentially_defect": "Poten<PERSON>ll defekt", "t_tcu_state_potentially_defect_description": "Die Telematik-Einheit ist nicht bestimmungsgemäß offline. Datenquelle: Signale, Events", "t_tcu_state_power_saving": "Sleep-Mode", "t_tcu_state_power_saving_description": "Die Telematik-Einheit befindet sich im Power-Saving-Modus. Datenquelle: Signale, Events", "t_tcu_state_temporary_not_available": "Te<PERSON>rär nicht verfügbar", "t_tcu_state_temporary_not_available_description": "Konnektivität ist kurzfristig unterbrochen. Datenquelle: Signale", "t_tcu_state_updating": "Updating", "t_tcu_state_updating_description": "Die Telematik-Einheit ist aufgrund eines laufenden Software-Updates temporär nicht einsatzbereit. Datenquelle: Bestandsdaten", "t_technical": "Technisch", "t_telematic_control_unit": "Telematik-Einheit", "t_telematic_type_distribution": "Verteilung Telematik-Einheit", "t_telematic_type_histogram": "Entwicklung Telematik-Einheit", "t_temperature_in_degree_celsius": "Temperatur in °C", "t_text_field_copied_text": "Text wurde kopiert", "t_this_can_not_be_undone": "Dies kann nicht rückgängig gemacht werden.", "t_this_field_cannot_be_empty": "<PERSON><PERSON>ld darf nicht leer sein", "t_this_view_cannot_be_filtered": "<PERSON><PERSON> Ansicht kann nicht gefiltert werden", "t_this_week": "<PERSON><PERSON>", "t_thursday": "Donnerstags", "t_thursdays": "Donnerstage", "t_timeperiod": "Zeitraum", "t_timestamp": "Zeitpunkt", "t_today": "<PERSON><PERSON>", "t_tool": "Tool", "t_total": "Gesamt", "t_tuesday": "Dienstags", "t_tuesdays": "Dienstage", "t_unknown": "Unbekannt", "t_user": "<PERSON><PERSON><PERSON>", "t_user_create": "<PERSON><PERSON><PERSON>", "t_user_create_success": "Benutzer erfolgreich erstellt", "t_user_create_title": "Neuen Benutzer erstellen", "t_user_delete_success": "Benutzer erfolgreich <PERSON>", "t_user_edit": "<PERSON><PERSON><PERSON> bearbeiten", "t_user_edit_success": "Benutzer erfolgreich bearbeitet", "t_user_edit_title": "<PERSON><PERSON><PERSON> bearbeiten", "t_user_email": "Email", "t_user_email_already_taken": "Email-Adresse bereits vergeben", "t_user_email_is_required": "<PERSON><PERSON> Email-<PERSON><PERSON><PERSON> an<PERSON>ben", "t_user_last_login": "<PERSON><PERSON><PERSON>", "t_user_locale": "<PERSON><PERSON><PERSON>", "t_user_locale_is_required": "Bitte Sprache wählen", "t_user_management": "Benutzerverwaltung", "t_user_management_exit": "Benutzerverwaltung verlassen", "t_user_reset_password": "Passwort zurücksetzen", "t_user_reset_password_description_1": "Das Passwort des gewählten Benutzers wird zurückgesetzt.", "t_user_reset_password_description_2": "Im Anschlus<PERSON> wird der Benutzer per E-Mail aufgefordert ein neues Passwort zu vergeben.", "t_user_reset_password_title": "Passwort zurücksetzen", "t_user_role": "Subscription", "t_user_status": "Status", "t_user_status_not_verified": "Nicht verifiziert", "t_user_status_verified": "Verifiziert", "t_user_transfer_add_user_description": "Benutzer suchen zum Hinzufügen", "t_user_transfer_empty_list": "<PERSON><PERSON> gefunden", "t_users": "<PERSON><PERSON><PERSON>", "t_value": "Wert", "t_variant": "<PERSON><PERSON><PERSON><PERSON>", "t_vehicle_count": "<PERSON><PERSON><PERSON> Fahrzeuge", "t_vehicle_status": "Status", "t_vehicle_status_depending_on_gps_position": "Fahrzeugstatus laut GPS-Position", "t_vehicle_status_in_depot": "Im Depot", "t_vehicle_status_in_depot_description": "Entfernung zum Depot < 1km", "t_vehicle_status_in_production": "In Produktion", "t_vehicle_status_in_production_description": "Fahrzeug ist einem Produktions-Standort zugeordnet", "t_vehicle_status_inactive_description": "Entfernung zum Depot zwischen 1km und 80km, letzte Bewegungsdaten > 12h oder eingesteckt", "t_vehicle_status_on_track": "Unterwegs", "t_vehicle_status_on_track_description": "Entfernung zum Depot zwischen 1km und 80km, letzte Bewegungsdaten < 12h, nicht e<PERSON><PERSON><PERSON>t", "t_vehicle_status_out_of_service": "<PERSON><PERSON><PERSON>", "t_vehicle_status_out_of_service_description": "Fahrzeug ist außer Betrieb gesetzt", "t_vehicle_status_test_vehicle": "Test Fahrzeug", "t_vehicle_status_test_vehicle_description": "Fahrzeug ist einem Test-Standort zugeordnet", "t_vehicle_status_undefined": "Unbekannt", "t_vehicle_status_undefined_description": "Entfernung zum Depot > 1000km oder keine GPS-Daten", "t_vehicle_status_wrong_depot_assignment": "Falsches Depot", "t_vehicle_status_wrong_depot_assignment_description": "Entfernung zum Depot zwischen 80km und 1000km, letzte Fahrt < 72h oder unterwegs", "t_vehicle_transfer_csv_import_error": "Fehler bein Einlesen der CSV-Datei.", "t_vehicle_transfer_csv_import_success": "CSV-Date<PERSON> erfolgreich eingelesen.", "t_vehicles": "Fahrzeuge", "t_vehicles_count": "<PERSON><PERSON><PERSON> Fahrzeuge", "t_version": "Version", "t_view_battery": "Batterieansicht", "t_view_configuration": "<PERSON><PERSON><PERSON> an<PERSON>", "t_view_default": "Standard-Ansicht", "t_view_fleet": "Flottenansicht", "t_view_technical": "Technische-Ansicht", "t_view_vehicle": "Fahrzeugansicht", "t_vin": "VIN", "t_vlv": "Fahrzeug Live-Ansicht", "t_vlv_drivers_log": "Fahrtenbuch", "t_vlv_signals_data_points_max": "<PERSON><PERSON>", "t_vlv_signals_error_too_much_datapoints": "Zu viele Datenpunkte", "t_vlv_signals_header_extended_settings": "Erweiterte Einstellungen", "t_vlv_signals_sampling_rate_max": "Maximale Auflösung", "t_vlv_status": "Live-Status", "t_wednesday": "Mittwochs", "t_wednesdays": "Mittwoche", "t_week": "KW", "t_week_view": "Wochenansicht", "t_weekdays": "Wochentage", "t_weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_weeks_12": "12 Wochen", "t_weeks_4": "4 Wochen", "t_weeks_8": "8 Wochen", "t_white": "<PERSON><PERSON>", "t_wiki": "Wiki", "t_within": "Innerhalb", "t_year_view": "Jahresansicht", "t_yellow": "<PERSON><PERSON><PERSON>", "t_yes": "<PERSON>a", "t_yesterday": "Gestern", "t_zb_plural": "ZBs", "t_zsp_plural": "ZSPs", "t_energy_efficiency": "Energieeffizienz", "t_total_energy_used": "Gesamtenergieverbrauch", "t_select_battery": "Batterie auswählen", "t_no_battery_selected": "<PERSON><PERSON> Batterie ausgewählt", "t_battery_measurements": "Batteriemessungen", "t_select_measurements": "Messungen auswählen", "t_battery_measurement_soh": "Gesundheitszustand (SOH)", "t_soh_overview": "SOH", "t_fleet_average_soh": "Durchschnittlicher Flotten-SOH", "t_critical_batteries": "Kritische Batterien", "t_warning_batteries": "Warnung Batterien", "t_healthy_batteries": "Gesunde Batterien", "t_degradation_rate": "Degradationsrate", "t_soh_distribution": "SOH Verteilung", "t_fleet_soh_trend": "Flotten SOH Trend (12 Monate)", "t_battery_soh_analysis": "Batterie SOH Analyse", "t_trend": "Trend", "t_days_since_measurement": "Tage seit letzter Messung", "t_predicted_replacement": "Voraussichtlicher Austausch", "t_action_required": "Erforderliche Aktion", "t_replace": "<PERSON><PERSON><PERSON><PERSON>", "t_monitor": "Überwachen", "t_ok": "OK", "t_immediate_actions": "Sofortige Maßnahmen", "t_planned_maintenance": "<PERSON><PERSON><PERSON> (30 Tage)", "t_optimization_opportunities": "Optimierungsmöglichkeiten", "t_battery_status_evaluation": "Batteriestatus-Bewertung", "t_soh_condition": "SOH", "t_excellent": "Ausgezeichnet", "t_good": "Gut", "t_not_assigned": "<PERSON>cht zugewiesen", "t_n_a": "k.A.", "batteries": "Batterien", "t_battery_measurement_safety_score": "Sicherheitswert", "t_battery_charts": "Messungen", "t_select_date_range": "Datumsbereich auswählen", "t_event_logbook": "Ereignislogbuch", "t_battery_was_previously_used_in": "Batterie {batteryId} war in folgenden Fahrzeugen verbaut:", "t_from": "von", "t_until": "bis", "t_battery_installation_history": "Batterieinstallationen", "t_no_installation_history_found": "Keine Installationshistorie gefunden", "t_no_installation_history_found_subtitle": "<PERSON><PERSON><PERSON> diese Batterie liegt keine Installationshistorie vor"}