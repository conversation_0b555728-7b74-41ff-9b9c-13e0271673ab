import React from "react";
import { Row, <PERSON> } from "antd";
import { snakeCase } from "lodash";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";

import { NumberField, DurationField } from "misc/fields";
import { StatusBoxWidget } from "misc/widgets/consumers";
import { PieChartWidget } from "misc/widgets/consumers";

import { stsColors } from "styles";

const DrivingBehaviorTemplate = ({ baseUrl, filter = {}, params = {} }) => {
    const [t] = useTranslation();

    return (
        <Row gutter={[10, 10]}>
            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_rides_count"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "ridesCount", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <NumberField value={value} />}
                    icon={["fas", "tally"]}
                />
            </Col>

            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_avg_trip_duration"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "averageTripTime", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <DurationField duration={value} />}
                    icon={["fas", "stopwatch"]}
                />
            </Col>

            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_covered_distance"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "coveredDistance", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <NumberField value={value} suffix="km" decimals={1} />}
                    icon={["fas", "road"]}
                />
            </Col>

            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_stops"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "stopsCount", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <NumberField value={value} />}
                    icon={["fas", "person-carry-box"]}
                />
            </Col>

            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_speed_max"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "maxSpeed", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <NumberField value={value} suffix="km/h" decimals={1} />}
                    icon={["fas", "tachometer-alt-fastest"]}
                />
            </Col>

            <Col xs={24} lg={12} xl={8}>
                <StatusBoxWidget
                    title="t_speed_avg"
                    request={{
                        url: `${baseUrl}/values`,
                        params: { type: "averageSpeed", ...params },
                        filter: filter
                    }}
                    extractData="value"
                    extractLastUpdate="timestamp"
                    renderValue={value => <NumberField value={value} suffix="km/h" decimals={1} />}
                    icon={["fas", "tachometer-alt"]}
                />
            </Col>

            <Col xs={24} sm={12}>
                <PieChartWidget
                    title="t_drivemode"
                    request={{
                        url: `${baseUrl}/widgets/pie-chart`,
                        params: { type: "drivemodeDistribution", ...params },
                        filter: filter
                    }}
                    extractData="data"
                    extractLastUpdate={data => data.lastUpdate}
                    chartConfig={{
                        angleField: "value",
                        colorField: "key",
                        color: ({ key }) => {
                            switch (key) {
                                case "drivemodeEco":
                                    return stsColors.green2;
                                case "drivemodeDrive":
                                    return stsColors.blue2;
                                case "drivemodeReverse":
                                    return stsColors.blue1;
                                case "drivemodeNeutral":
                                default:
                                    return stsColors.grey1;
                            }
                        },
                        meta: {
                            value: {
                                formatter: v => Math.round(v / 3600)
                            },
                            key: {
                                formatter: v => t(`t_${snakeCase(v)}`)
                            }
                        },
                        label: {
                            type: "outer",
                            formatter: v => `${t(`t_${snakeCase(v.key)}`)}: ${Math.round(v.value / 3600)}h / ${Math.round(v.percent * 100)}%`
                        },
                        statistic: {
                            title: {
                                formatter: () => t("t_total")
                            },
                            content: {
                                formatter: (_, items) => {
                                    const sumHours = items.reduce((acc, item) => acc + item.value, 0);
                                    return Math.round(sumHours / 3600) + "h";
                                }
                            }
                        },
                        empty: {
                            isEmpty: data => data.length < 1 || !data.some(item => item.value > 0),
                            title: "t_no_data",
                            subTitle: "t_no_data_available"
                        }
                    }}
                />
            </Col>
            <Col xs={24} sm={12}>
                <PieChartWidget
                    title="t_driving_vs_standing_time"
                    request={{
                        url: `${baseUrl}/widgets/pie-chart`,
                        params: { type: "drivingStandingDistribution", ...params },
                        filter: filter
                    }}
                    extractData="data"
                    extractLastUpdate={data => data.lastUpdate}
                    chartConfig={{
                        angleField: "value",
                        colorField: "key",
                        color: ({ key }) => (key === "drivingTime" ? stsColors.blue2 : key === "standingTime" && stsColors.grey1),
                        meta: {
                            value: {
                                formatter: v => Math.round(v / 3600)
                            },
                            key: {
                                formatter: v => t(`t_${snakeCase(v)}`)
                            }
                        },
                        label: {
                            type: "outer",
                            formatter: v => `${t(`t_${snakeCase(v.key)}`)}: ${Math.round(v.value / 3600)}h / ${Math.round(v.percent * 100)}%`
                        },
                        statistic: {
                            title: {
                                formatter: () => t("t_total")
                            },
                            content: {
                                formatter: (_, items) => {
                                    const sumHours = items.reduce((acc, item) => acc + item.value, 0);
                                    return Math.round(sumHours / 3600) + "h";
                                }
                            }
                        },
                        empty: {
                            isEmpty: data => data.length < 1 || !data.some(item => item.value > 0),
                            title: "t_no_data",
                            subTitle: "t_no_data_available"
                        }
                    }}
                />
            </Col>
        </Row>
    );
};

DrivingBehaviorTemplate.propTypes = {
    baseUrl: PropTypes.string.isRequired,
    params: PropTypes.object,
    filter: PropTypes.object
};

export default DrivingBehaviorTemplate;
