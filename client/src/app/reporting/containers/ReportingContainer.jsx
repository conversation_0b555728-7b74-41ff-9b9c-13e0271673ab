import React from "react";
import { Switch, Route, Redirect } from "react-router-dom";
import { useQueries } from "react-query";

import { reportingUrls, clientUrls } from "misc/urls";

import { ErrorWidget } from "misc/widgets/consumers";
import { CheckAppPermissions } from "misc/authentication";

import ReportsListContainer from "./ReportsListContainer";
import ReportContainer from "./ReportContainer";

const ReportingContainer = () => {
    return "under construction";

    // const dispatch = useDispatch();
    // const { error } = useQueries([
    //     {
    //         queryKey: [{ url: reportingUrls.reports() }],
    //         onSuccess: data => {
    //             dispatch({ type: "SET_REPORTS", reports: data.reports });
    //         }
    //     },
    //     {
    //         queryKey: [{ url: reportingUrls.templates() }],
    //         onSuccess: data => {
    //             dispatch({ type: "SET_TEMPLATES", reports: data.reportTemplates });
    //         }
    //     }
    // ]);

    // if (error) return <ErrorWidget error={error} />;
    // return (
    //     <CheckAppPermissions allowed={["permission_reporting"]}>
    //         <Switch>
    //             <Route exact path={clientUrls.reporting.baseUrl()} component={ReportsListContainer} />
    //             <Route exact path={clientUrls.reporting.create()} component={ReportContainer} />
    //             <Route exact path={clientUrls.reporting.edit()} component={ReportContainer} />

    //             <Redirect to={clientUrls.reporting.baseUrl()} />
    //         </Switch>
    //     </CheckAppPermissions>
    // );
};

export default ReportingContainer;
