import React, { useCallback } from "react";
import { useHistory } from "react-router";
import { useTranslation } from "react-i18next";
import { message } from "antd";

import { useRequest } from "misc/hooks";

import { clientUrls, reportingUrls } from "misc/urls";
import { buildUrl } from "misc/helpfunctions";

import { getReports } from "reporting/redux/selectors";

import ReportsListComponent from "../components/ReportsListComponent";

const ReportsListContainer = () => {
    return "under construction";

    // const [t] = useTranslation();

    // const dispatch = useDispatch();
    // const history = useHistory();

    // const reports = useSelector(getReports);

    // const [loading, performRequests] = useRequest();

    // const editReport = useCallback(
    //     ({ reportId }) => {
    //         history.push(buildUrl({ path: clientUrls.reporting.edit(), queryParameters: { reportId } }));
    //     },
    //     [history]
    // );

    // const deleteReport = useCallback(
    //     ({ reportId }) =>
    //         performRequests({ method: "DELETE", url: reportingUrls.reports() + reportId }, data => {
    //             dispatch({ type: "DELETE_REPORT", deletedReport: data.deletedReport });
    //             message.success(t("t_report_deleted"), 1);
    //         }),
    //     [dispatch, performRequests, t]
    // );

    // const data = { reports, loading };
    // const functions = { editReport, deleteReport };

    // return <ReportsListComponent data={data} functions={functions} />;
};

export default ReportsListContainer;
