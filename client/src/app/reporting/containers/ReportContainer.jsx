import React, { useCallback } from "react";
import { useHistory, useLocation } from "react-router";

import { useRequest } from "misc/hooks";

import { reportingUrls, clientUrls } from "misc/urls";
import { getQueryParameter } from "misc/helpfunctions";

import { getReport } from "reporting/redux/selectors";

import ReportComponent from "../components/ReportComponent";

const ReportContainer = () => {
    return "under construction";

    // const dispatch = useDispatch();

    // const location = useLocation();
    // const history = useHistory();

    // const reportId = getQueryParameter(location, "reportId");

    // const report = useSelector(state => getReport(state, reportId));

    // const [loading, performRequests] = useRequest();

    // const createReport = useCallback(
    //     data =>
    //         performRequests(
    //             {
    //                 method: "POST",
    //                 url: reportingUrls.reports(),
    //                 body: JSON.stringify({
    //                     name: data.name,
    //                     subfleet: data.subfleet,
    //                     cronString: data.cronString,
    //                     templates: data.templates
    //                 })
    //             },
    //             data => {
    //                 dispatch({ type: "ADD_REPORT", createdReport: data.createdReport });
    //                 history.push(clientUrls.reporting.baseUrl());
    //             }
    //         ),
    //     [dispatch, history, performRequests]
    // );

    // const updateReport = useCallback(
    //     data =>
    //         performRequests(
    //             {
    //                 method: "PUT",
    //                 url: `${reportingUrls.reports()}${reportId}/`,
    //                 body: JSON.stringify({
    //                     name: data.name,
    //                     subfleet: data.subfleet,
    //                     cronString: data.cronString,
    //                     templates: data.templates
    //                 })
    //             },
    //             data => {
    //                 dispatch({ type: "UPDATE_REPORT", updatedReport: data.updatedReport });
    //                 history.push(clientUrls.reporting.baseUrl());
    //             }
    //         ),
    //     [dispatch, history, performRequests, reportId]
    // );

    // const data = { report, loading };
    // const functions = { createReport, updateReport };

    // return <ReportComponent data={data} functions={functions} />;
};

export default ReportContainer;
