const initialState = {
    reports: [],
    templates: []
};

export const reportingReducer = (state = initialState, action) => {
    const reports = [...(state?.reports ?? [])];

    switch (action.type) {
        case "SET_TEMPLATES":
            return {
                ...state,
                templates: action.templates
            };

        case "SET_REPORTS":
            return {
                ...state,
                reports: action.reports
            };

        case "ADD_REPORT": {
            return { ...state, reports: [...reports, action.createdReport] };
        }

        case "UPDATE_REPORT": {
            const index = reports.findIndex(report => report.reportId === action.updatedReport.reportId);
            reports[index] = action.updatedReport;

            return { ...state, reports };
        }

        case "DELETE_REPORT": {
            return { ...state, reports: reports.filter(report => report.reportId !== parseFloat(action.deletedReport)) };
        }

        case "DELETE_REDUX_STATE":
            return {};

        default:
            return state;
    }
};
