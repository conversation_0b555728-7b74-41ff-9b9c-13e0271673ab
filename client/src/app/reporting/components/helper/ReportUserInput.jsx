import React from "react";
import { Select } from "antd";

const { Option } = Select;

const ReportUserInput = ({ availableUsers, users, onChange }) => (
    <Select style={{ width: "100%" }} defaultValue={users} mode="multiple" onChange={onChange} loading={availableUsers === undefined}>
        {availableUsers.map(user => (
            <Option key={user.id} value={user.username}>
                {user.username}
            </Option>
        ))}
    </Select>
);

ReportUserInput.defaultProps = {
    availableUsers: [],
    users: []
};

export default ReportUserInput;
