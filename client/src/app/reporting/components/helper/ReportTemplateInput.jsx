import React from "react";
import { useTranslation } from "react-i18next";
import { Select } from "antd";

import { TextField } from "misc/fields";

import { getTemplates } from "reporting/redux/selectors";

const { Option } = Select;

const ReportTemplateInput = ({ value, onChange }) => {
    return "underconstruction";

    // const [t] = useTranslation();

    // const templates = useSelector(getTemplates);

    // return (
    //     <Select placeholder={t("t_select_template")} value={value} onChange={onChange} mode="multiple" style={{ width: "100%" }}>
    //         {templates.map(template => (
    //             <Option key={template.reportTemplateId} value={template.reportTemplateId}>
    //                 <TextField value={`t_${template.name}`} />
    //             </Option>
    //         ))}
    //     </Select>
    // );
};

export default ReportTemplateInput;
