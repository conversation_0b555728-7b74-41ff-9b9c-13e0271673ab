import React from "react";
import { Select } from "antd";

const { Option } = Select;

const ReportRolesInput = ({ availableRoles, roles, onChange }) => (
    <Select
        style={{ width: "100%" }}
        placeholder="Rollen"
        mode="multiple"
        defaultValue={roles}
        onChange={onChange}
        loading={availableRoles === undefined}
    >
        {availableRoles.map(({ id, title, name }) => (
            <Option key={id} value={name}>
                {title}
            </Option>
        ))}
    </Select>
);

ReportRolesInput.defaultProps = {
    availableRoles: [],
    roles: []
};

export default ReportRolesInput;
