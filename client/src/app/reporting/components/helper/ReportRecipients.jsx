import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Select, Input } from "antd";

import ReportUserInput from "./ReportUserInput";
// import ReportRolesInput from "./ReportRolesInput";

const { Option } = Select;

const Only = ({ when, children }) => Boolean(when) && children;

const ReportRecipients = ({ addressees, availableUsers, availableRoles, onChange }) => {
    return "under construction";
    // const [t] = useTranslation();
    // const [users, setUsers] = useState([]);
    // const [roles, setRoles] = useState([]);
    // const [type, setType] = useState(undefined);
    // const isUsers = () => type === "users";
    // // const isRoles = () => type === "roles";
    // useEffect(() => {
    //     setType(addressees.type);
    //     if (addressees.type === "users") setUsers(addressees.entries);
    //     if (addressees.type === "roles") setRoles(addressees.entries);
    // }, [addressees.entries, addressees.type]);
    // useEffect(() => {
    //     if (type) {
    //         const entries = type === "users" ? users : roles;
    //         onChange({
    //             type: type,
    //             entries: entries
    //         });
    //     }
    // }, [type, users, roles, onChange]);
    // const handleChangeButton = value => setType(value);
    // return (
    //     <Input.Group compact>
    //         <Select
    //             placeholder={t("t_select_recipients")}
    //             style={{ width: "100%" }}
    //             value={!type || type === "" ? undefined : type}
    //             onChange={handleChangeButton}
    //         >
    //             <Option value="users">{t("t_user")}</Option>
    //             {/* <Option value="roles">{t("t_roles")}</Option> */}
    //         </Select>
    //         <Only when={isUsers()}>
    //             <ReportUserInput availableUsers={availableUsers} users={users} onChange={setUsers} />
    //         </Only>
    //         {/* <Only when={isRoles()}>
    //             <ReportRolesInput availableRoles={availableRoles} roles={roles} onChange={setRoles} />
    //         </Only> */}
    //     </Input.Group>
    // );
};

const mapStateToProps = state => ({
    availableUsers: getUsers(state),
    availableRoles: getAvailableDataRoles(state)
});

export default connect(mapStateToProps, null)(ReportRecipients);
