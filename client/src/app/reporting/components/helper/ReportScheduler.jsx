import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Input, Select } from "antd";

import { TextField } from "misc/fields";

import { decode, encode } from "misc/cronParser";

const WeekDayInput = ({ weekDayNumber, onChange }) => {
    const [t] = useTranslation();

    const weekDays = [
        { number: 1, day: "t_monday" },
        { number: 2, day: "t_tuesday" },
        { number: 3, day: "t_wednesday" },
        { number: 4, day: "t_thursday" },
        { number: 5, day: "t_friday" },
        { number: 6, day: "t_saturday" },
        { number: 0, day: "t_sunday" }
    ];

    return (
        <Select style={{ width: "100%" }} value={weekDayNumber && `${weekDayNumber}`} onChange={onChange} placeholder={t("t_select_weekday")}>
            {weekDays.map(({ number, day }) => (
                <Select.Option key={day} value={`${number}`}>
                    <TextField value={day} />
                </Select.Option>
            ))}
        </Select>
    );
};

const MonthlyDateInput = ({ monthDayNumber, onChange }) => {
    const [t] = useTranslation();

    const DAYS_IN_MONTH = 28;
    const daysOfMonth = Array.from(Array(DAYS_IN_MONTH).keys(), key => key + 1);

    return (
        <Select value={monthDayNumber} onChange={onChange} placeholder={t("t_select_monthday")} style={{ width: "100%" }}>
            {daysOfMonth.map(day => (
                <Select.Option key={day} value={day}>
                    <TextField value={`${day}.`} />
                </Select.Option>
            ))}
        </Select>
    );
};

const ReportScheduler = ({ initialValue, value, onChange }) => {
    const [t] = useTranslation();

    const [day, setDay] = useState();
    const [rhythm, setRhythm] = useState(value);

    useEffect(() => {
        if (initialValue) {
            const rhythm = decode(initialValue);
            // eslint-disable-next-line
            const [minutes, hours, dayOfMonth, month, dayOfWeek] = initialValue.split(/\s/);

            if (rhythm === "daily") {
                setRhythm("daily");
            } else if (rhythm === "weekly") {
                setRhythm("weekly");
                setDay(dayOfWeek);
            } else if (rhythm === "monthly") {
                setRhythm("monthly");
                setDay(dayOfMonth);
            }
        }
    }, [initialValue]);

    useEffect(() => rhythm && onChange(encode(rhythm, day)), [rhythm, day, onChange]);

    const handleChangeRhythm = useCallback(
        value => {
            setDay(undefined);
            setRhythm(value);
        },
        [setDay, setRhythm]
    );

    return (
        <Input.Group compact>
            <Select style={{ width: "100%" }} placeholder={t("t_select_rhythm")} value={rhythm} onChange={handleChangeRhythm}>
                <Select.Option value="daily">
                    <TextField value="t_daily" />
                </Select.Option>
                <Select.Option value="weekly">
                    <TextField value="t_weekly" />
                </Select.Option>
                <Select.Option value="monthly">
                    <TextField value="t_monthly" />
                </Select.Option>
            </Select>

            {rhythm === "weekly" && <WeekDayInput weekDayNumber={day} onChange={setDay} />}
            {rhythm === "monthly" && <MonthlyDateInput monthDayNumber={day} onChange={setDay} />}
        </Input.Group>
    );
};

export default ReportScheduler;
