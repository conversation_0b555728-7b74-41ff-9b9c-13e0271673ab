import React, { useCallback } from "react";
import { Form, Input, Row, Col, Space } from "antd";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { TextField } from "misc/fields";
import { PrimaryButton, SubmitButton } from "misc/buttons";
import { Widget } from "misc/widgets";

import { PermissionConfigurator } from "misc/configurators";

import { clientUrls } from "misc/urls";

import ReportScheduler from "./helper/ReportScheduler";
import ReportTemplateInput from "./helper/ReportTemplateInput";

const ReportComponent = ({ data, functions }) => {
    const [form] = Form.useForm();
    const [t] = useTranslation();

    const onClickSubmit = useCallback(() => {
        form.validateFields().then(values => {
            if (data.report) functions.updateReport(values);
            else functions.createReport(values);
        });
    }, [form, data.report, functions]);

    return (
        <Widget title="t_create_report">
            <Row gutter={[64, 64]}>
                <Col span={24}>
                    <Form
                        form={form}
                        initialValues={{
                            name: data.report?.name,
                            subfleet: data.report?.subfleet.map(element => element),
                            cronString: data.report?.cronString,
                            templates: data.report?.templates.map(template => template.reportTemplateId)
                        }}
                        layout="vertical"
                    >
                        <Form.Item
                            name="name"
                            label={t("t_report_name")}
                            rules={[{ required: true, message: <TextField value="t_missing_name_message" /> }]}
                        >
                            <Input placeholder="enter_report_name" />
                        </Form.Item>

                        <Form.Item
                            name="subfleet"
                            label={t("t_subfleet")}
                            rules={[{ required: true, message: <TextField value="t_select_subfleet_message" /> }]}
                        >
                            <PermissionConfigurator width="100%" />
                        </Form.Item>

                        <Form.Item name="cronString" label={t("t_rhythm")} rules={[{ required: true, message: t("t_select_rhythm_message") }]}>
                            <ReportScheduler />
                        </Form.Item>

                        <Form.Item name="templates" label={t("t_content")} rules={[{ required: true, message: t("t_select_template_message") }]}>
                            <ReportTemplateInput />
                        </Form.Item>
                    </Form>
                </Col>

                <Col span={24} align="right">
                    <Space>
                        <Link to={{ pathname: clientUrls.reporting.baseUrl() }}>
                            <PrimaryButton>t_cancel</PrimaryButton>
                        </Link>

                        <SubmitButton onClick={onClickSubmit} loading={data.loading}>
                            t_save
                        </SubmitButton>
                    </Space>
                </Col>
            </Row>
        </Widget>
    );
};

export default ReportComponent;
