import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Tag, Tooltip, Table } from "antd";

import { PrimaryButton } from "misc/buttons";
import { TextField } from "misc/fields";
import { Widget } from "misc/widgets";

import { clientUrls } from "misc/urls";

import { decode } from "misc/cronParser";

const ReportsListComponent = ({ data, functions }) => {
    const [t] = useTranslation();

    const columns = useMemo(
        () => [
            {
                title: t("t_name"),
                dataIndex: "name",
                sorter: (a, b) => a.name.localeCompare(b.name),
                ellipsis: true,
                width: "22.5%"
            },
            {
                title: t("t_rhythm"),
                dataIndex: "cronString",
                ellipsis: true,
                render: text => <TextField value={decode(text)} />,
                width: "22.5%"
            },
            {
                title: t("t_subfleet"),
                dataIndex: "subfleet",
                ellipsis: true,
                render: (text, record) =>
                    record.subfleet.map(element => (
                        <Tooltip title={element.namePath}>
                            <Tag>
                                <TextField value={element} />
                            </Tag>
                        </Tooltip>
                    )),
                width: "22.5%"
            },
            {
                title: t("t_content"),
                dataIndex: "templates",
                ellipsis: true,
                render: (text, record) =>
                    record.templates.map(template => (
                        <Tag>
                            <TextField value={`t_${template.name}`} />
                        </Tag>
                    )),
                width: "22.5%"
            }
        ],
        [t]
    );

    return (
        <Widget
            title="t_reports"
            extra={
                data.reports.length && (
                    <PrimaryButton>
                        <Link to={clientUrls.reporting.create()}>
                            <TextField value="t_add" />
                        </Link>
                    </PrimaryButton>
                )
            }
            style={{ height: 600 }}
        >
            <Table
                data={data.reports}
                rowKey="reportId"
                columns={columns}
                loading={data.loading}
                pagination={{
                    total: data.reports.length
                }}
                actions={[
                    { action: "edit", onCallAction: functions.editReport },
                    { action: "delete", onCallAction: functions.deleteReport }
                ]}
            />
        </Widget>
    );
};

const report = PropTypes.shape({
    reportId: PropTypes.number.isRequired,
    cronString: PropTypes.string.isRequired,
    templates: PropTypes.arrayOf(
        PropTypes.shape({
            templateId: PropTypes.number.isRequired,
            name: PropTypes.string.isRequired
        }).isRequired
    ),
    subfleet: PropTypes.arrayOf(
        PropTypes.shape({
            name: PropTypes.string.isRequired,
            path: PropTypes.string.isRequired,
            namePath: PropTypes.string.isRequired
        }).isRequired
    )
});

ReportsListComponent.propTypes = {
    data: PropTypes.shape({
        reports: PropTypes.oneOfType([PropTypes.array, PropTypes.arrayOf(report)]).isRequired,
        loading: PropTypes.bool.isRequired
    }).isRequired,
    functions: PropTypes.shape({
        editReport: PropTypes.func.isRequired,
        deleteReport: PropTypes.func.isRequired
    })
};

export default ReportsListComponent;
