import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Collapse, Space, Button, message, Descriptions } from "antd";

import { Card } from "misc/card";
import { TextField, DateField } from "misc/fields/";
import { LanguageSelector } from "misc/selectors";
import { useLocale, useLoggedInUser, useUpdateLocale, useResetPassword } from "misc/api/keycloakApi";

const SettingsComponent = () => {
    const [t] = useTranslation();

    const { locale } = useLocale();
    const { loggedInUser } = useLoggedInUser({ suspense: true });

    const { updateLocale, isLoading: isUpdatingLanguage } = useUpdateLocale();
    const { resetPassword, isLoading: isResetingPassword } = useResetPassword();

    const role = useMemo(() => {
        const name = loggedInUser.roles?.at(0).name.replace("role_subscription_", "");
        const expiration = loggedInUser.roles?.at(0).attributes?.find(attribute => attribute.name === "expiration")?.value;

        return {
            name: name,
            expiration: expiration
        };
    }, [loggedInUser]);

    const onSelectLanguage = value =>
        updateLocale(value, {
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });

    const onResetPassword = value => {
        resetPassword(loggedInUser, {
            onSuccess: () => message.success(t("t_reset_password_email_sent")),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    return (
        <Card title="t_header_user_settings" height={700}>
            <Collapse accordion>
                <Collapse.Panel header={<TextField value="t_settings_role" inline />} key="settings-role">
                    <Descriptions column={1} labelStyle={{ width: "20%" }} contentStyle={{ width: "80%" }} bordered>
                        <Descriptions.Item label={<TextField value="t_settings_role_name" />}>
                            <TextField value={role.name} />
                        </Descriptions.Item>
                        <Descriptions.Item label={<TextField value="t_settings_role_expiration" />}>
                            {role.expiration === "unlimited" ? (
                                <TextField value={`t_role_expiration_${role.expiration}`} />
                            ) : (
                                <DateField value={role.expiration} />
                            )}
                        </Descriptions.Item>
                    </Descriptions>
                </Collapse.Panel>
                <Collapse.Panel header={<TextField value="t_settings_language" inline />} key="settings-language">
                    <Space direction="vertical" size={10}>
                        <TextField value="t_settings_select_language" />

                        <LanguageSelector
                            value={locale.default}
                            onChange={onSelectLanguage}
                            loading={isUpdatingLanguage}
                            style={{ width: 200 }}
                            showSearch
                        />
                    </Space>
                </Collapse.Panel>
                <Collapse.Panel header={<TextField value="t_settings_password" inline />} key="settings-password">
                    <Space direction="vertical" size={10}>
                        <TextField value="t_settings_send_email_for_password_reset" />

                        <Button type="primary" onClick={onResetPassword} loading={isResetingPassword}>
                            <TextField value="t_settings_reset_password" inline />
                        </Button>
                    </Space>
                </Collapse.Panel>
            </Collapse>
        </Card>
    );
};

export default SettingsComponent;
