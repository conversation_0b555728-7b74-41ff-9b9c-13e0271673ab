import { Switch, Redirect, Route } from "react-router-dom";

import { clientUrls } from "misc/urls/clientUrls";
import { CheckAppPermissions } from "misc/authentication";

import AdminUserComponent from "admin/components/AdminUserComponent";
import AdminRoleComponent from "admin/components/AdminRoleComponent";
import AdminUsersListComponent from "admin/components/AdminUsersListComponent";
import AdminRolesListComponent from "admin/components/AdminRolesListComponent";

const AdminContainer = () => {
    return (
        <CheckAppPermissions allowed={["permission_module_user-management"]}>
            <Switch>
                <Route exact path={clientUrls.userManagement.users.baseUrl()} component={AdminUsersListComponent} />
                <Route exact path={clientUrls.userManagement.users.create()} component={AdminUserComponent} />
                <Route exact path={clientUrls.userManagement.users.edit()} component={AdminUserComponent} />
                <Route exact path={clientUrls.userManagement.roles.baseUrl()} component={AdminRolesListComponent} />
                <Route exact path={clientUrls.userManagement.roles.create()} component={AdminRoleComponent} />
                <Route exact path={clientUrls.userManagement.roles.edit()} component={AdminRoleComponent} />
                <Redirect to={clientUrls.userManagement.users.baseUrl()} />
            </Switch>
        </CheckAppPermissions>
    );
};

export default AdminContainer;
