import moment from "moment";
import styled from "styled-components";
import React, { useState } from "react";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip, Button, Modal, Space, message, Descriptions, Tag } from "antd";

import { Card } from "misc/card";
import { EditableTable } from "misc/tables";
import { buildUrl } from "misc/helpfunctions";
import { clientUrls } from "misc/urls/clientUrls";
import { DatetimeField, TextField } from "misc/fields/";
import { useDeleteUser, useResetPassword, useRoles, useUsers } from "misc/api/keycloakApi";

import { stsColors } from "styles";
import SideHeader from "navigation/SideHeader";

const UserIcon = styled(FontAwesomeIcon)({
    color: stsColors.grey3
});

const AdminUsersListComponent = () => {
    const [t] = useTranslation();
    const history = useHistory();

    const { users, isLoading: isLoadingUsers, isRefetching: isRefetchingUsers } = useUsers();
    const { roles } = useRoles();

    const { deleteUser, isLoading: isDeletingUser } = useDeleteUser();
    const { resetPassword, isLoading: isResettingPasswort } = useResetPassword();

    const [userForPasswordReset, setUserForPasswordReset] = useState(null);

    const onDeleteUser = user =>
        deleteUser(user, {
            onSuccess: () => message.success(t("t_user_delete_success")),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });

    const onResetPassword = user => {
        resetPassword(user, {
            onSuccess: () => message.success(t("t_reset_password_email_sent")),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    const expandedRowRender = rowData => {
        const userSubscriptions = rowData.roles.filter(({ name }) => name.includes("role_subscription_"));

        const mappedRoles = roles.filter(({ id: roleId }) => userSubscriptions.some(({ id }) => roleId === id));

        const permissions = mappedRoles.reduce(
            (result, { permissions }) => ({
                organisationalUnits: [
                    ...result.organisationalUnits,
                    ...permissions
                        .filter(({ name }) => name.includes("permission_data"))
                        .map(({ name, description }) => (description ? description : name.replace("permission_data_", "").replace("-", "_")))
                ],
                manufacturers: [
                    ...result.manufacturers,
                    ...permissions
                        .filter(({ name }) => name.includes("permission_manufacturer"))
                        .map(({ name, description }) =>
                            description ? description : t(`t_manufacturer_${name.replace("permission_manufacturer_", "").replace("-", "_")}`)
                        )
                ],
                modules: [
                    ...result.modules,
                    ...permissions
                        .filter(({ name }) => name.includes("permission_module"))
                        .map(({ name, description }) =>
                            description ? description : t(`t_module_${name.replace("permission_module_", "").replace("-", "_")}`)
                        )
                ],
                features: [
                    ...result.features,
                    ...permissions
                        .filter(({ name }) => name.includes("permission_feature"))
                        .map(({ name, description }) =>
                            description ? description : t(`t_feature_${name.replace("permission_feature_", "").replace("-", "_")}`)
                        )
                ]
            }),
            { organisationalUnits: [], manufacturers: [], modules: [], features: [] }
        );

        return (
            <Card>
                <Descriptions
                    title={
                        userSubscriptions.length ? (
                            <TextField
                                prefix={t("t_role")}
                                value={userSubscriptions
                                    .map(subscription => subscription.name)
                                    .join(", ")
                                    .replace("role_subscription_", "")}
                            />
                        ) : (
                            <TextField value="t_role_missing" />
                        )
                    }
                    bordered
                >
                    <Descriptions.Item label={<TextField value="t_role_organisational_units" />} span={24} labelStyle={{ width: 300 }}>
                        {permissions.organisationalUnits.map(organisationalUnit => (
                            <Tag>{organisationalUnit}</Tag>
                        ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value="t_role_manufacturers" />} span={24} labelStyle={{ width: 300 }}>
                        {permissions.manufacturers.map(manufacturer => (
                            <Tag>{manufacturer}</Tag>
                        ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value="t_role_modules" />} span={24} labelStyle={{ width: 300 }}>
                        {permissions.modules.map(module => (
                            <Tag>{module}</Tag>
                        ))}
                    </Descriptions.Item>

                    <Descriptions.Item label={<TextField value="t_role_features" />} span={24} labelStyle={{ width: 300 }}>
                        {permissions.features.map(feature => (
                            <Tag>{feature}</Tag>
                        ))}
                    </Descriptions.Item>
                </Descriptions>
            </Card>
        );
    };

    return (
        <SideHeader>
            <Card title="t_users" height={700} stretchHeight>
                <EditableTable
                    data={users.filter(user => user.email).map(user => ({ ...user, editable: true, deletable: true }))}
                    columns={[
                        {
                            key: "enabled",
                            dataIndex: "emailVerified",
                            title: <TextField value="t_user_status" />,
                            render: text =>
                                text ? (
                                    <Tooltip title={<TextField value="t_user_status_verified" />}>
                                        <UserIcon icon="user-check" />
                                    </Tooltip>
                                ) : (
                                    <Tooltip title={<TextField value="t_user_status_not_verified" />}>
                                        <UserIcon icon="user-lock" />
                                    </Tooltip>
                                ),
                            filterSearch: true,
                            filters: [
                                { text: t("t_user_status_verified"), value: true },
                                { text: t("t_user_status_not_verified"), value: false }
                            ],
                            onFilter: (value, record) => record.emailVerified === value,
                            width: "10%"
                        },
                        {
                            key: "email",
                            dataIndex: "email",
                            title: <TextField value="t_user_email" />,
                            sorter: (a, b) => a.email.localeCompare(b.email),
                            defaultSortOrder: "ascend",
                            width: "25%"
                        },
                        {
                            key: "roles",
                            dataIndex: "roles",
                            title: <TextField value="t_subscription" />,
                            render: (text = []) =>
                                text
                                    .filter(({ name }) => name.includes("role_subscription"))
                                    .map(({ name }) => name?.replace("role_subscription_", ""))
                                    .join(", "),
                            filterSearch: true,
                            filters: roles.map(role => ({ text: role.name.replace("role_subscription_", ""), value: role.name })),
                            onFilter: (value, record) => record.roles.some(role => role.name === value),
                            width: "25%"
                        },
                        {
                            key: "lastLogin",
                            dataIndex: "lastLogin",
                            title: <TextField value="t_user_last_login" />,
                            render: value => (value ? <Tooltip title={<DatetimeField value={value} />}>{moment(value).fromNow()}</Tooltip> : "-"),
                            sorter: (a, b) => moment(b.lastLogin ?? 0).unix() - moment(a.lastLogin ?? 0).unix()
                        }
                    ]}
                    expandable={{ expandedRowRender }}
                    rowKey="id"
                    onDeleteItem={onDeleteUser}
                    filterItems={(filter, item) => item.email.toLowerCase().includes(filter.toLowerCase())}
                    extraActions={record => [
                        {
                            disabled: !record.editable,
                            tooltip: "t_user_edit",
                            key: `edit ${record.id}`,
                            icon: <FontAwesomeIcon icon={["fal", "pen-to-square"]} />,
                            onClick: () =>
                                history.push(
                                    buildUrl({
                                        path: clientUrls.userManagement.users.edit(),
                                        queryParameters: { userId: record.id }
                                    })
                                )
                        },
                        {
                            icon: <FontAwesomeIcon icon={["fal", "key"]} />,
                            onClick: () => setUserForPasswordReset(record),
                            tooltip: "t_user_reset_password",
                            key: `reset ${record.id}`
                        }
                    ]}
                    extra={
                        <Button onClick={() => history.push(clientUrls.userManagement.users.create())}>
                            <Space>
                                <FontAwesomeIcon icon={["fal", "plus"]} />
                                <TextField value="t_user_create" />
                            </Space>
                        </Button>
                    }
                    isLoading={isLoadingUsers}
                    isUpdating={isRefetchingUsers || isDeletingUser || isResettingPasswort}
                    pagination={{
                        defaultPageSize: 20,
                        pageSizeOptions: [10, 20, 50, 100],
                        total: users.length,
                        showSizeChanger: true,
                        showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />,
                        onShowSizeChange: () => window.scrollTo(0, 0)
                    }}
                />
                <Modal
                    title={<TextField value="t_user_reset_password_title" />}
                    visible={userForPasswordReset}
                    onCancel={() => setUserForPasswordReset(null)}
                    onOk={() => {
                        onResetPassword(userForPasswordReset);
                        setUserForPasswordReset(null);
                    }}
                    cancelText={<TextField value="t_cancel" />}
                    closable
                >
                    <Space direction="vertical" size={0}>
                        <TextField value="t_user_reset_password_description_1" />
                        <TextField value="t_user_reset_password_description_2" />
                    </Space>
                </Modal>
            </Card>
        </SideHeader>
    );
};

export default AdminUsersListComponent;
