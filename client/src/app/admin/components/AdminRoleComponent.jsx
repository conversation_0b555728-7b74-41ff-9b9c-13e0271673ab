import React, { useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { useHistory, useLocation } from "react-router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { List, Row, Form, Space, Avatar, Button, message } from "antd";

import { Card } from "misc/card";
import { clientUrls } from "misc/urls";
import { TextField } from "misc/fields";
import { getQueryParameter } from "misc/helpfunctions";
import { useRoles, useCreateRole, useUpdateRole } from "misc/api/keycloakApi";

import { fonts } from "styles";
import SideHeader from "navigation/SideHeader";

import RoleName from "../components/steps/RoleName";
import RoleUsers from "../components/steps/RoleUsers";
import RoleModules from "../components/steps/RoleModules";
import RoleManufacturers from "../components/steps/RoleManufacturers";
import RoleOrganisationalUnits from "../components/steps/RoleOrganisationalUnits";
import RoleExpiration from "./steps/RoleExpiration";
import RoleFeatures from "./steps/RoleFeatures";

const Title = styled(TextField)({
    fontSize: fonts.size16
});

const AdminRoleComponent = () => {
    const [t] = useTranslation();

    const [form] = Form.useForm();

    const location = useLocation();
    const history = useHistory();

    const { roles, isLoading: isLoadingRoles } = useRoles({ suspense: true });

    const { createRole, isLoading: isCreatingRole } = useCreateRole();
    const { updateRole, isLoading: isUpdatingRole } = useUpdateRole();

    const roleId = useMemo(() => getQueryParameter(location, "roleId"), [location]);
    const role = useMemo(() => roles.find(role => role.id === roleId), [roles, roleId]);

    const steps = useMemo(
        () => [
            {
                key: "role-name",
                title: "t_role_name",
                icon: "Aa",
                content: <RoleName form={form} name="name" value={role?.name.replace("role_subscription_", "") ?? ""} />
            },
            {
                key: "role-expiration",
                title: "t_role_expiration",
                icon: <FontAwesomeIcon icon={["fas", "calendar-day"]} />,
                content: (
                    <RoleExpiration
                        form={form}
                        name="expiration"
                        value={role?.attributes?.find(attribute => attribute.name === "expiration")?.value ?? null}
                    />
                )
            },
            {
                key: "role-manufacturers",
                title: "t_role_manufacturers",
                icon: <FontAwesomeIcon icon={["fas", "industry-windows"]} />,
                content: (
                    <RoleManufacturers
                        form={form}
                        name="manufacturers"
                        value={
                            role?.permissions
                                .filter(role => role.name.includes("permission_manufacturer_"))
                                .map(role => role.name.replace("permission_manufacturer_", "")) ?? []
                        }
                    />
                )
            },
            {
                key: "role-organisational-units",
                title: "t_role_organisational_units",
                icon: <FontAwesomeIcon icon={["fas", "sitemap"]} />,
                content: (
                    <RoleOrganisationalUnits
                        form={form}
                        name="organisationalUnits"
                        value={
                            role?.permissions
                                .filter(role => role.name.includes("permission_data_"))
                                .map(role => role.name.replace("permission_data_", "")) ?? []
                        }
                    />
                )
            },
            {
                key: "role-modules",
                title: "t_role_modules",
                icon: <FontAwesomeIcon icon={["fas", "list-ul"]} />,
                content: (
                    <RoleModules
                        form={form}
                        name="modules"
                        value={
                            role?.permissions
                                .filter(role => role.name.includes("permission_module_"))
                                .map(role => role.name.replace("permission_module_", "")) ?? []
                        }
                    />
                )
            },
            {
                key: "role-features",
                title: "t_role_features",
                icon: <FontAwesomeIcon icon={["fas", "plus"]} />,
                content: (
                    <RoleFeatures
                        form={form}
                        name="features"
                        value={
                            role?.permissions
                                .filter(role => role.name.includes("permission_feature_"))
                                .map(role => role.name.replace("permission_feature_", "")) ?? []
                        }
                    />
                )
            },
            {
                key: "role-users",
                title: "t_role_users",
                icon: <FontAwesomeIcon icon={["fas", "user"]} />,
                content: <RoleUsers form={form} name="users" value={role?.users.map(({ id }) => id) ?? []} />
            }
        ],
        [form, role]
    );

    const onClickSave = values => {
        const payload = {
            name: `subscription_${values.name}`,
            permissions: [
                ...values.manufacturers.map(manufacturer => `permission_manufacturer_${manufacturer}`),
                ...values.organisationalUnits.map(organisationalUnit => `permission_data_${organisationalUnit}`),
                ...values.modules.map(module => `permission_module_${module}`),
                ...values.features.map(module => `permission_feature_${module}`)
            ],
            users: values.users,
            expiration: values.expiration
        };

        role ? onEditRole({ id: role.id, ...payload }) : onCreateRole(payload);
    };

    const onCreateRole = role => {
        createRole(role, {
            onSuccess: () => message.success(t("t_role_create_success")) && history.push(clientUrls.userManagement.roles.baseUrl()),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    const onEditRole = role => {
        updateRole(role, {
            onSuccess: () => message.success(t("t_role_edit_success")) && history.push(clientUrls.userManagement.roles.baseUrl()),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    return (
        <SideHeader>
            <Card title={roleId ? "t_role_edit" : "t_role_create_title"} isLoading={isLoadingRoles} height={700} stretchHeight>
                <List
                    split={false}
                    dataSource={steps}
                    renderItem={item => (
                        <List.Item key={item.key}>
                            <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                <Space size={12}>
                                    <Avatar size={40}>{item.icon}</Avatar>
                                    <Title value={item.title} />
                                </Space>
                                {item.content}
                            </Space>
                        </List.Item>
                    )}
                />
                <Row type="flex" justify="end">
                    <Form form={form} onFinish={onClickSave} scrollToFirstError>
                        <Space direction="horizontal" size={3}>
                            <Link to={clientUrls.userManagement.roles.baseUrl()}>
                                <Button>
                                    <TextField value="t_cancel" />
                                </Button>
                            </Link>

                            <Button type="primary" htmlType="submit" loading={isCreatingRole || isUpdatingRole}>
                                <TextField value="t_save" />
                            </Button>
                        </Space>
                    </Form>
                </Row>
            </Card>
        </SideHeader>
    );
};

export default AdminRoleComponent;
