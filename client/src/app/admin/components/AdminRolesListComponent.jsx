import React from "react";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Descriptions, message, Space, List, Tag } from "antd";

import { Card } from "misc/card";
import { EditableTable } from "misc/tables";
import { buildUrl } from "misc/helpfunctions";
import { clientUrls } from "misc/urls/clientUrls";
import { DateField, TextField } from "misc/fields";
import { useRoles, useDeleteRole } from "misc/api/keycloakApi";
import SideHeader from "navigation/SideHeader";

const AdminRolesListComponent = () => {
    const [t] = useTranslation();
    const history = useHistory();

    const { roles, isLoading: isLoadingRoles, isRefetching: isRefetchingRoles } = useRoles();

    const { deleteRole, isLoading: isDeletingRole } = useDeleteRole();

    const onDeleteRole = role => {
        deleteRole(role, {
            onSuccess: () => message.success(t("t_role_deleted")),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    const expandedRowRender = record => {
        return (
            <Card>
                <Descriptions
                    title={<TextField value="t_role_details" />}
                    column={1}
                    labelStyle={{ width: "20%" }}
                    contentStyle={{ width: "80%" }}
                    bordered
                >
                    <Descriptions.Item label={<TextField value={"t_role_manufacturers"} />}>
                        {record.permissions
                            .filter(permission => permission.name.startsWith("permission_manufacturer_"))
                            .map(permission => (
                                <Tag>
                                    {permission.description
                                        ? permission.description
                                        : t(`t_manufacturer_${permission.name.replace("permission_manufacturer_", "").replace("-", "_")}`)}
                                </Tag>
                            ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value={"t_role_organisational_units"} />}>
                        {record.permissions
                            .filter(permission => permission.name.startsWith("permission_data_"))
                            .map(permission => (
                                <Tag>{permission.description ? permission.description : permission.name.replace("permission_data_", "")}</Tag>
                            ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value={"t_role_modules"} />}>
                        {record.permissions
                            .filter(permission => permission.name.startsWith("permission_module_"))
                            .map(permission => (
                                <Tag>
                                    {permission.description
                                        ? permission.description
                                        : t(`t_module_${permission.name.replace("permission_module_", "").replace("-", "_")}`)}
                                </Tag>
                            ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value={"t_role_features"} />}>
                        {record.permissions
                            .filter(permission => permission.name.startsWith("permission_feature_"))
                            .map(permission => (
                                <Tag>
                                    {permission.description
                                        ? permission.description
                                        : t(`t_feature_${permission.name.replace("permission_feature_", "").replace("-", "_")}`)}
                                </Tag>
                            ))}
                    </Descriptions.Item>
                    <Descriptions.Item label={<TextField value="t_role_users" />}>
                        <List
                            rowKey="id"
                            dataSource={record.users.sort((a, b) => a.email?.localeCompare(b.email))}
                            renderItem={item => <List.Item>{item.email}</List.Item>}
                        />
                    </Descriptions.Item>
                </Descriptions>
            </Card>
        );
    };

    return (
        <SideHeader>
            <Card title="t_roles" height={700} stretchHeight>
                <EditableTable
                    data={roles.map(role => ({ ...role, editable: true, deletable: true }))}
                    columns={[
                        {
                            title: <TextField value="t_role_name" />,
                            dataIndex: "name",
                            render: text => text.replace("role_subscription_", ""),
                            sorter: (a, b) => a.name.localeCompare(b.name),
                            defaultSortOrder: "ascend",
                            search: true,
                            width: 250
                        },
                        {
                            title: <TextField value="t_role_expiration" />,
                            dataIndex: "attributes",
                            render: attributes => {
                                const value = attributes.find(attribute => attribute.name === "expiration")?.value ?? null;

                                return value === "unlimited" ? <TextField value={`t_role_expiration_${value}`} /> : <DateField value={value} />;
                            },
                            width: 160
                        },
                        {
                            title: <TextField value="t_role_users" />,
                            dataIndex: "users",
                            render: users => users.length
                        },
                        {
                            title: <TextField value="t_role_manufacturers" />,
                            dataIndex: "permissions",
                            render: permissions => permissions.filter(permission => permission.name.startsWith("permission_manufacturer_")).length,
                            width: 160
                        },
                        {
                            title: <TextField value="t_role_organisational_units" />,
                            dataIndex: "permissions",
                            render: permissions => permissions.filter(permission => permission.name.startsWith("permission_data_")).length,
                            width: 200
                        },
                        {
                            title: <TextField value="t_role_modules" />,
                            dataIndex: "permissions",
                            render: permissions => permissions.filter(permission => permission.name.startsWith("permission_module_")).length,
                            width: 160
                        },
                        {
                            title: <TextField value="t_role_features" />,
                            dataIndex: "permissions",
                            render: permissions => permissions.filter(permission => permission.name.startsWith("permission_feature_")).length,
                            width: 180
                        }
                    ]}
                    rowKey="id"
                    onDeleteItem={onDeleteRole}
                    filterItems={(filter, item) => item.name.toLowerCase().includes(filter.toLowerCase())}
                    extraActions={record => [
                        {
                            icon: <FontAwesomeIcon icon={["fal", "pen-to-square"]} />,
                            disabled: !record.editable,
                            tooltip: "t_role_edit",
                            key: `edit ${record.id}`,
                            onClick: () =>
                                history.push(
                                    buildUrl({
                                        path: clientUrls.userManagement.roles.edit(),
                                        queryParameters: { roleId: record.id }
                                    })
                                )
                        }
                    ]}
                    extra={
                        <Button onClick={() => history.push(clientUrls.userManagement.roles.create())}>
                            <Space>
                                <FontAwesomeIcon icon={["fal", "plus"]} />
                                <TextField value="t_role_create" />
                            </Space>
                        </Button>
                    }
                    expandable={{ expandedRowRender }}
                    isLoading={isLoadingRoles}
                    isUpdating={isRefetchingRoles || isDeletingRole}
                />
            </Card>
        </SideHeader>
    );
};

export default AdminRolesListComponent;
