import styled from "styled-components";
import React, { useMemo } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useHistory, useLocation } from "react-router";
import { List, Row, Form, Space, Avatar, Button, message } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { Card } from "misc/card";
import { clientUrls } from "misc/urls";
import { TextField } from "misc/fields";
import { getQueryParameter } from "misc/helpfunctions";
import { useCreateUser, useUpdateUser, useUsers } from "misc/api/keycloakApi";

import UserRole from "../components/steps/UserRole";
import UserEmail from "../components/steps/UserEmail";
import UserLocale from "../components/steps/UserLocale";

import { fonts } from "styles";
import SideHeader from "navigation/SideHeader";

const Title = styled(TextField)({
    fontSize: fonts.size16
});

const AdminUserComponent = () => {
    const [t] = useTranslation();

    const location = useLocation();
    const history = useHistory();

    const [form] = Form.useForm();

    const { users, isLoading: isLoadingUsers } = useUsers({ suspense: true });

    const { createUser, isLoading: isCreatingUser } = useCreateUser();
    const { updateUser, isLoading: isUpdatingUser } = useUpdateUser();

    const user = useMemo(() => users.find(user => user.id === getQueryParameter(location, "userId")), [users, location]);

    const steps = useMemo(() => {
        const subscription = user?.roles?.find(role => role.name.startsWith("role_subscription_"))?.name;

        return [
            {
                key: "user-email",
                title: "t_user_email",
                icon: <FontAwesomeIcon icon={["fas", "user"]} />,
                content: <UserEmail form={form} name="email" value={user?.email} />
            },
            {
                key: "user-locale",
                title: "t_user_locale",
                icon: <FontAwesomeIcon icon={["fas", "language"]} />,
                content: <UserLocale form={form} name="locale" value={user?.locale} />
            },
            {
                key: "user-role",
                title: "t_user_role",
                icon: <FontAwesomeIcon icon={["fas", "money-check-pen"]} />,
                content: <UserRole form={form} name="role" value={subscription} />
            }
        ];
    }, [form, user]);

    const onClickSave = values => {
        const payload = {
            email: values.email,
            locale: values.locale,
            roles: values.role ? [values.role] : []
        };

        user ? onEditUser({ id: user.id, ...payload }) : onCreateUser(payload);
    };

    const onCreateUser = user => {
        createUser(user, {
            onSuccess: () => message.success(t("t_user_create_success")) && history.push(clientUrls.userManagement.users.baseUrl()),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    const onEditUser = user => {
        updateUser(user, {
            onSuccess: () => message.success(t("t_user_edit_success")) && history.push(clientUrls.userManagement.users.baseUrl()),
            onError: error => message.error(t(`t_http_error_${error.status}`))
        });
    };

    return (
        <SideHeader>
            <Card height={800} stretchHeight title={user ? "t_user_edit_title" : "t_user_create_title"} isLoading={isLoadingUsers}>
                <List
                    split={false}
                    dataSource={steps}
                    renderItem={item => (
                        <List.Item key={item.key}>
                            <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                <Space size={12}>
                                    <Avatar size={40}>{item.icon}</Avatar>
                                    <Title value={item.title} />
                                </Space>
                                {item.content}
                            </Space>
                        </List.Item>
                    )}
                />

                <Row type="flex" justify="end">
                    <Form form={form} onFinish={onClickSave}>
                        <Form.Item>
                            <Space direction="horizontal" size={3}>
                                <Link to={clientUrls.userManagement.users.baseUrl()}>
                                    <Button>
                                        <TextField value="t_cancel" />
                                    </Button>
                                </Link>
                                <Button type="primary" htmlType="submit" loading={isCreatingUser || isUpdatingUser}>
                                    <TextField value="t_save" />
                                </Button>
                            </Space>
                        </Form.Item>
                    </Form>
                </Row>
            </Card>
        </SideHeader>
    );
};

export default AdminUserComponent;
