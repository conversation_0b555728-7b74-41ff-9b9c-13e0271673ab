import React from "react";
import { Form } from "antd";
import { useTranslation } from "react-i18next";

import { Selector } from "misc/selectors";
import { useRoles } from "misc/api/keycloakApi";

const RoleSelector = props => {
    const { roles } = useRoles({ suspense: true });

    return (
        <Selector
            options={roles.map(role => ({ key: role.id, value: role.name, label: role.name.replace("role_subscription_", "") }))}
            showSearch
            {...props}
        />
    );
};

const UserRole = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item name={name}>
                <RoleSelector placeholder={t("t_user_role")} />
            </Form.Item>
        </Form>
    );
};

export default UserRole;
