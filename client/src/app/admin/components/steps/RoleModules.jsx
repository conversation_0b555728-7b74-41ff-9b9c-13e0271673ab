import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { ModulesTransfer } from "misc/transfer";

const RoleModules = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item
                name={name}
                rules={[
                    {
                        required: true,
                        message: t("t_role_modules_is_required")
                    }
                ]}
            >
                <ModulesTransfer />
            </Form.Item>
        </Form>
    );
};

RoleModules.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.array
};

export default RoleModules;
