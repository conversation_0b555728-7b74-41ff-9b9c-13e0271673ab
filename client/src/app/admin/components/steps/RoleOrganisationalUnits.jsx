import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

import { OrganisationalUnitTransfer } from "misc/transfer";

const RoleOrganisationalUnits = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item
                name={name}
                rules={[
                    {
                        required: true,
                        message: t("t_role_organisational_units_is_required")
                    }
                ]}
            >
                <OrganisationalUnitTransfer />
            </Form.Item>
        </Form>
    );
};

RoleOrganisationalUnits.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.array
};

export default RoleOrganisationalUnits;
