import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

import { LanguageSelector } from "misc/selectors";

const UserLocale = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item name={name} rules={[{ required: true, message: t("t_user_locale_is_required") }]}>
                <LanguageSelector placeholder={t("t_user_locale")} />
            </Form.Item>
        </Form>
    );
};

UserLocale.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.string
};

export default UserLocale;
