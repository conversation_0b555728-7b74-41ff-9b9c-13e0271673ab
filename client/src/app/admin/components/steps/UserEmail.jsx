import PropTypes from "prop-types";
import { Input, Form } from "antd";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useUsers } from "misc/api/keycloakApi";

const UserEmail = ({ form, name, value }) => {
    const [t] = useTranslation();

    const { users } = useUsers();

    const emails = useMemo(() => users.map(user => user.email), [users]);

    return (
        <Form form={form} initialValues={{ [name]: value }} disabled={value}>
            <Form.Item
                name={name}
                rules={[
                    {
                        type: "email",
                        required: true,
                        message: t("t_user_email_is_required")
                    },
                    {
                        message: t("t_user_email_already_taken"),
                        validator: (_, currentValue) => {
                            // No validation on duplicate because email is not editable
                            if (value) return Promise.resolve();
                            return emails.includes(currentValue) ? Promise.reject() : Promise.resolve();
                        }
                    }
                ]}
            >
                <Input placeholder={t("t_user_email")} style={{ width: "100%" }} disabled={value} />
            </Form.Item>
        </Form>
    );
};

UserEmail.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.string
};

export default UserEmail;
