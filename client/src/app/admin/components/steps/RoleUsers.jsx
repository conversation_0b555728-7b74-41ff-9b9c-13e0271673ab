import PropTypes from "prop-types";
import { Form, Tooltip } from "antd";
import React, { useMemo } from "react";
import { useLocation } from "react-router";
import { useTranslation } from "react-i18next";

import { UserTransfer } from "misc/transfer";
import { getQueryParameter } from "misc/helpfunctions";

const RoleUsers = ({ form, name, value }) => {
    const [t] = useTranslation();

    const location = useLocation();

    const roleId = useMemo(() => getQueryParameter(location, "roleId"), [location]);

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item name={name}>
                <UserTransfer
                    disableItem={item => item.roles?.filter(role => role.name.startsWith("role_subscription_") && role.id !== roleId).length}
                    render={record =>
                        record.disabled ? (
                            <Tooltip title={t("t_role_user_already_has_subscription")} placement="left">
                                {record.email}
                            </Tooltip>
                        ) : (
                            record.email
                        )
                    }
                />
            </Form.Item>
        </Form>
    );
};

RoleUsers.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.array
};

export default RoleUsers;
