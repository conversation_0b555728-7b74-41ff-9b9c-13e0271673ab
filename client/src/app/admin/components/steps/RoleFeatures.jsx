import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";

import { FeatureTransfer } from "misc/transfer";

const RoleFeatures = ({ form, name, value }) => {
    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item name={name}>
                <FeatureTransfer />
            </Form.Item>
        </Form>
    );
};

RoleFeatures.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.array
};

export default RoleFeatures;
