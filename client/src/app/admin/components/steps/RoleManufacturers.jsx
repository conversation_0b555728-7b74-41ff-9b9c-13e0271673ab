import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

import { ManufacturerTransfer } from "misc/transfer";

const RoleManufacturers = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item
                name={name}
                rules={[
                    {
                        required: true,
                        message: t("t_role_manufacturers_is_required")
                    }
                ]}
            >
                <ManufacturerTransfer />
            </Form.Item>
        </Form>
    );
};

RoleManufacturers.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.array
};

export default RoleManufacturers;
