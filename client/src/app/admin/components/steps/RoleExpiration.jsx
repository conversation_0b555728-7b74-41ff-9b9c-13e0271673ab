import React from "react";
import { Form } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

import { ExpirationSelector } from "misc/selectors";

const RoleExpiration = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item
                name={name}
                rules={[
                    {
                        required: true,
                        message: t("t_role_expiration_is_required")
                    }
                ]}
            >
                <ExpirationSelector />
            </Form.Item>
        </Form>
    );
};

RoleExpiration.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.string
};

export default RoleExpiration;
