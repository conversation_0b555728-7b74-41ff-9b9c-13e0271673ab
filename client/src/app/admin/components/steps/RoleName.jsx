import React from "react";
import PropTypes from "prop-types";
import { Input, Form } from "antd";
import { useTranslation } from "react-i18next";

const RoleName = ({ form, name, value }) => {
    const [t] = useTranslation();

    return (
        <Form form={form} initialValues={{ [name]: value }}>
            <Form.Item
                name={name}
                rules={[
                    {
                        type: "string",
                        required: true,
                        message: t("t_role_name_is_required")
                    }
                ]}
            >
                <Input placeholder={t("t_role_name_placeholder")} />
            </Form.Item>
        </Form>
    );
};

RoleName.propTypes = {
    form: PropTypes.any.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.string
};

export default RoleName;
