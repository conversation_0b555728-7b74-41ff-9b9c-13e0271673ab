// Available theme variables can be found in
// https://github.com/ant-design/ant-design/blob/master/components/style/themes/default.less

// @import (inline) "colors.less";
// colors
@transparent: rgba(255, 255, 255, 0);
@aliceblue: #f0f3ff;
@ceil: #93a2dd;

@green1: #90be6d;
@green2: #337f4c;
@green3: #337f4c;

@yellow1: #f9c74f;
@yellow2: #f8961e;
@yellow3: #f3722c;

@red1: #f94144;
@red2: #d00000;
@red3: #9d0208;

@grey1: #d9d9d9;
@grey2: #a1a1a1;
@grey3: #707070;

@blue1: #6d8ea0;
@blue2: #155292;

@white1: #ffffff;
@white2: #f8f9fa;
@white3: #f5f5f5;

@black1: #505050;
@black2: #393939;
@black3: #000000;

// // Overwrite antd defaults
// @primary-color: @blue2;
// @success-color: @blue2;
// @info-color: @blue2;
// @processing-color: @blue2;
// @error-color: @red2;
// @warning-color: @yellow2;
// @border-color-base: @grey1;
// @avatar-color: @white1;
// @avatar-bg: @blue2;
// @border-radius-base: 4px;
// @badge-status-size: 8px;

// // body
// @font-family: "Open Sans", sans-serif;
// @body-background: #efeff5;
// @font-size-base: 14px;
// @line-height-base: 1.5;
// @text-color: @black2;
// @text-color-secondary: @black1;

// // link
// @link-color: "unset";
// @link-hover-color: "unset";

// // layout-header
// @layout-header-height: unset;
// @layout-header-padding: 0;
// @layout-header-background: unset;

// // layout-sider
// @layout-sider-background: @blue2;
// @layout-trigger-background: @blue2;

// // layout-body
// @layout-body-background: transparent;

// // layout-footer
// @layout-footer-padding: 0;
// @layout-footer-background: transparent;

// // breadcrumb
// @breadcrumb-base-color: unset;
// @breadcrumb-last-item-color: unset;
// @breadcrumb-link-color: unset;
// @breadcrumb-link-color-hover: unset;
// @breadcrumb-separator-color: unset;

// // popover
// @popover-bg: @white1;
// @popover-color: unset;

// // menu
// @menu-dark-bg: @blue2;
// @menu-dark-submenu-bg: @blue2;
// @menu-dark-item-active-bg: darken(@blue2, 10%);
// @menu-dark-highlight-color: @white1;

// // card
// @card-radius: 4px;
// @card-background: @white1;
// @card-padding-base: 30px;
// @card-head-padding: 15px;
// @card-head-background: transparent;
// @card-head-color: @black2;
// @card-actions-background: transparent;

// // button
// @btn-primary-bg: @blue2;
// @btn-primary-color: @white1;
// @btn-font-weight: 600;
// @btn-shadow: none;
// @btn-primary-shadow: @btn-shadow;
// @btn-text-shadow: @btn-shadow;

// // input
// @input-hover-border-color: @border-color-base;

// // input number
// @input-number-hover-border-color: @border-color-base;

// // alert
// @alert-error-border-color: @red2;
// @alert-error-bg-color: @red2;
// @alert-error-icon-color: @white1;

// @alert-info-border-color: @grey1;
// @alert-info-bg-color: @white3;
