import { rest } from "msw";

export const handlers = [
    // rest.get("http://localhost:8001/services/data/fleet/widgets/list/", (req, res, ctx) => {
    //     const type = req.url.searchParams.get("type");
    //     // if (type === "chargingMonitorDashboardExport") {
    //     //     return res(
    //     //         ctx.delay(2000),
    //     //         ctx.json({
    //     //             chargingMonitorDashboardExport: {
    //     //                 data: [
    //     //                     {
    //     //                         date: "2022-09-01",
    //     //                         case_1_1_1: 23,
    //     //                         case_1_1_2: 56,
    //     //                         case_1_2_1: 21,
    //     //                         case_1_2_2: 23,
    //     //                         case_1_3_1: 68,
    //     //                         case_1_3_2: 2,
    //     //                         case_1_4_1: 46,
    //     //                         case_1_4_2: 8,
    //     //                         case_1_5: 21,
    //     //                         case_2_1_1: 35,
    //     //                         case_2_1_2: 6,
    //     //                         case_2_2_1: 44,
    //     //                         case_2_2_2: 90,
    //     //                         case_2_3_1: 8,
    //     //                         case_2_3_2: 66,
    //     //                         case_2_4: 34,
    //     //                         case_3_1: 12,
    //     //                         case_3_2: 31,
    //     //                         case_3_3: 35
    //     //                     }
    //     //                 ]
    //     //             }
    //     //         })
    //     //     );
    //     // }
    //     // if (type === "chargingMonitorFleetViewExport") {
    //     //     return res(
    //     //         ctx.delay(2000),
    //     //         ctx.json({
    //     //             chargingMonitorFleetViewExport: {
    //     //                 data: [
    //     //                     {
    //     //                         vin: "SPES17GAANB100045",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-01",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     },
    //     //                     {
    //     //                         vin: "SPES17GAANB100045",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-02",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTime: "2022-09-01 10:00:00",
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     },
    //     //                     {
    //     //                         vin: "SPES17GAANB100045",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-03",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTime: "2022-09-01 10:00:00",
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     },
    //     //                     {
    //     //                         vin: "SPES17GAANB100046",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-01",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTime: "2022-09-01 10:00:00",
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     },
    //     //                     {
    //     //                         vin: "SPES17GAANB100046",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-02",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTime: "2022-09-01 10:00:00",
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     },
    //     //                     {
    //     //                         vin: "SPES17GAANB100046",
    //     //                         licensePlate: "BN-I3669E",
    //     //                         manufacturer: "StreetScooter GmbH",
    //     //                         model: "E1803",
    //     //                         battery: "SDA",
    //     //                         lastSignOfLifeTimestamp: "2022-09-01 17:00:00",
    //     //                         lastSignOfLifeSignal: "Dummy data",
    //     //                         date: "2022-09-03",
    //     //                         chargingManagement: "LLW",
    //     //                         organisationalUnitType: "Operational",
    //     //                         properChargingStation: true,
    //     //                         connectivity: Math.floor(Math.random() * 100),
    //     //                         case: "1.1.1",
    //     //                         chargingStation: "Dummy data",
    //     //                         socStart: Math.floor(Math.random() * 100),
    //     //                         socEnd: Math.floor(Math.random() * 100),
    //     //                         departureTime: "2022-09-01 10:00:00",
    //     //                         departureTimeLlw: "2022-09-01 10:00:00",
    //     //                         socCharge: 15,
    //     //                         chargeCompleted: true,
    //     //                         chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     //                         organisationalUnit: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     //                         organisationalUnitLlw: "Dummy data",
    //     //                         distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     //                         telematicControlUnit: "Dummy data",
    //     //                         temperatureAmbientMin: 20,
    //     //                         temperatureAmbientMax: 26,
    //     //                         version: "0.3"
    //     //                     }
    //     //                 ]
    //     //             }
    //     //         })
    //     //     );
    //     // }
    //     // if (type === "chargingMonitorDashboard") {
    //     //     return res(
    //     //         ctx.delay(2000),
    //     //         ctx.json({
    //     //             chargingMonitorDashboard: {
    //     //                 data: [
    //     //                     {
    //     //                         date: "2022-09-01",
    //     //                         data: [
    //     //                             { case: "1.1.1", count: 1 },
    //     //                             { case: "1.1.2", count: 0 },
    //     //                             { case: "1.2.1", count: 0 },
    //     //                             { case: "1.2.2", count: 0 },
    //     //                             { case: "1.3.1", count: 0 },
    //     //                             { case: "1.3.2", count: 0 },
    //     //                             { case: "1.4.1", count: 0 },
    //     //                             { case: "1.4.2", count: 1 },
    //     //                             { case: "1.5", count: 2 },
    //     //                             { case: "2.1.1", count: 0 },
    //     //                             { case: "2.1.2", count: 0 },
    //     //                             { case: "2.2.1", count: 0 },
    //     //                             { case: "2.2.2", count: 0 },
    //     //                             { case: "2.3.1", count: 0 },
    //     //                             { case: "2.3.2", count: 0 },
    //     //                             { case: "2.4", count: 0 },
    //     //                             { case: "3.1", count: 0 },
    //     //                             { case: "3.2", count: 0 },
    //     //                             { case: "3.3", count: 0 }
    //     //                         ]
    //     //                     }
    //     //                 ]
    //     //             }
    //     //         })
    //     //     );
    //     // }
    //     // if (type === "chargingMonitorDashboard") {
    //     //     return res(
    //     //         ctx.delay(2000),
    //     //         ctx.json({
    //     //             chargingMonitorDashboard: {
    //     //                 data: [
    //     //                     {
    //     //                         case: "1.1.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.1.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.2.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.2.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.3.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.3.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.4.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.4.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "1.5",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.1.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.1.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.2.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.2.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.3.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.3.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "2.4",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.1.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.1.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.2.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.2.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.3.1",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.3.2",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     },
    //     //                     {
    //     //                         case: "3.4",
    //     //                         "2022-09-01": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-02": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-03": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-04": Math.floor(Math.random() * 1000),
    //     //                         "2022-09-05": Math.floor(Math.random() * 1000)
    //     //                     }
    //     //                 ]
    //     //             }
    //     //         })
    //     //     );
    //     // }
    // })
    // Fleet view (old): date per vehicle object
    // {
    //     date: "2022-09-04",
    //     chargingManagement: "LLW",
    //     organisationalUnitType: "Operational", // Operation, Production, Other
    //     properChargingStation: true,
    //     connectivity: Math.floor(Math.random() * 100),
    //     case: "1.1.1",
    //     chargingStation: "Dummy data",
    //     socStart: Math.floor(Math.random() * 100),
    //     socEnd: Math.floor(Math.random() * 100),
    //     departureTime: "2022-09-01 10:00:00",
    //     departureTimeLlw: "2022-09-01 10:00:00",
    //     socCharge: 15,
    //     chargeCompleted: true,
    //     chargeCompletedTimestamp: "2022-09-01 10:00:00",
    //     organisationalUnit: "Dummy data",
    //     distanceToAssignedOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     distanceToNearestOrganisationalUnit: Math.floor(Math.random() * 1000),
    //     organisationalUnitLlw: "Dummy data",
    //     distanceToAssignedOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     distanceToNearestOrganisationalUnitLlw: Math.floor(Math.random() * 1000),
    //     telematicControlUnit: "Dummy data",
    //     temperatureAmbientMin: 20,
    //     temperatureAmbientMax: 26,
    //     version: "0.3"
    // },
    // rest.get("https://api-staging.streetscooter-cloud-system.eu/services/data/fleet/widgets/list/", (req, res, ctx) => {
    //     const type = req.url.searchParams.get("type");
    //     if (type === "energyMonitorVehiclesView") {
    //         return res(
    //             ctx.delay(2000),
    //             ctx.json({
    //                 energyMonitorVehiclesView: {
    //                     statistics: { count: 20 },
    //                     data: [
    //                         {
    //                             vin: "SPES17FAANB100186",
    //                             licensePlate: "BN-I2854E",
    //                             manufacturer: "B-ON",
    //                             data: [
    //                                 {
    //                                     date: "total",
    //                                     mileageStart: 200,
    //                                     mileageEnd: 400,
    //                                     distance: 200,
    //                                     socCharged: 300,
    //                                     socConsumed: 400,
    //                                     energyConsumptionCharging: 600,
    //                                     energyConsumptionDriving: 500,
    //                                     temperatureMin: 2,
    //                                     temperatureMax: 12,
    //                                     temperatureMedian: 8,
    //                                     connectivity: 92,
    //                                     organisationalUnitFirst: "Köln mechZB",
    //                                     organisationalUnitLast: "Köln mechZB",
    //                                     chargingStationFirst: "UDB-1234",
    //                                     chargingStationLast: "UDB-1234",
    //                                     telematicControlUnitFirst: "tcu-1234",
    //                                     telematicControlUnitLast: "tcu-1234"
    //                                 },
    //                                 {
    //                                     date: "2022-11-07",
    //                                     socCharged: 200,
    //                                     socConsumed: 250
    //                                 },
    //                                 {
    //                                     date: "2022-11-14",
    //                                     socCharged: 200,
    //                                     socConsumed: 250
    //                                 }
    //                             ]
    //                         },
    //                         {
    //                             vin: "SPES17FAANB100187",
    //                             licensePlate: "BN-I2854E",
    //                             manufacturer: "B-ON",
    //                             data: [
    //                                 {
    //                                     date: "total",
    //                                     socCharged: 300,
    //                                     socConsumed: 400
    //                                 },
    //                                 {
    //                                     date: "2022-11-07",
    //                                     socCharged: 200,
    //                                     socConsumed: 250
    //                                 },
    //                                 {
    //                                     date: "2022-11-14",
    //                                     socCharged: 200,
    //                                     socConsumed: 250
    //                                 }
    //                             ]
    //                         }
    //                     ]
    //                 }
    //             })
    //         );
    //     }
    // })
    // rest.get("https://api-staging.streetscooter-cloud-system.eu/services/fleetconfiguration/subfleets/subfleets/", (req, res, ctx) => {
    //     return res(
    //         ctx.json([
    //             {
    //                 subfleetId: 100,
    //                 name: "Deutsche Post",
    //                 owner: "94f5e6a4-80c9-4258-adac-d46ec1b430db",
    //                 default: true,
    //                 vehicles: [],
    //                 organisationalUnits: ["deutsche-post"],
    //                 users: [],
    //                 parameters: {
    //                     cddaRange: ["2022-05-2010", "2022-05-17"],
    //                     cddaChargingSocRise: 25,
    //                     cddaDrivingDistance: 10,
    //                     cddaDeliveryDistance: 10,
    //                     cddaDeliveryStops: 10
    //                 }
    //             }
    //         ])
    //     );
    // })
];
